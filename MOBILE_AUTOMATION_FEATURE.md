# Mobile Automation Feature

## Overview
This document describes the new mobile automation feature that extends the existing web automation functionality to support mobile application testing.

## New Endpoints

The following new endpoints have been added to support mobile automation:

### POST `/projects/{projectId}/test-cases/tcId/{tcId}/automation-mobile`
- **Purpose**: Save mobile automation steps for a test case
- **Request Body**: `AutomationMobileTestCaseDto`
- **Response**: Created mobile automation test case
- **Status Codes**: 
  - 201: Mobile automation steps saved successfully
  - 404: Test case not found

### GET `/projects/{projectId}/test-cases/tcId/{tcId}/automation-mobile`
- **Purpose**: Get mobile automation steps for a test case
- **Response**: Mobile automation test case or empty steps array
- **Status Codes**:
  - 200: Mobile automation steps retrieved successfully
  - 404: Test case not found

### PATCH `/projects/{projectId}/test-cases/tcId/{tcId}/automation-mobile`
- **Purpose**: Update mobile automation steps for a test case
- **Request Body**: `AutomationMobileTestCaseDto`
- **Response**: Updated mobile automation test case
- **Status Codes**:
  - 200: Mobile automation steps updated successfully
  - 404: Test case not found

### POST `/projects/{projectId}/test-cases/{tcId}/automation-mobile/upload-file`
- **Purpose**: Upload a file for mobile automation test case step
- **Content Type**: `multipart/form-data`
- **Request Body**:
  - `file`: Binary file data
  - `stepId`: Step ID within the mobile automation test case
  - `testCaseId`: UUID of the test case
- **Response**: File upload result with URL and file ID
- **Status Codes**:
  - 201: Mobile automation file uploaded successfully
  - 400: Bad request - missing file or invalid parameters
  - 404: Test case not found

### DELETE `/projects/{projectId}/test-cases/{tcId}/automation-mobile/files/{fileId}`
- **Purpose**: Delete an uploaded file for mobile automation test case step
- **Parameters**:
  - `fileId`: Unique mobile file identifier
- **Response**: Deletion confirmation
- **Status Codes**:
  - 200: Mobile automation file deleted successfully
  - 404: Mobile automation file not found
  - 403: Unauthorized to delete this mobile automation file

## Mobile App File Management Endpoints

### POST `/projects/{projectId}/automation-mobile/upload-mobile-app`
- **Purpose**: Upload a mobile app file (APK/IPA) for a project
- **Content Type**: `multipart/form-data`
- **Request Body**:
  - `file`: Binary mobile app file (APK/IPA)
  - `platform`: Mobile platform (android/ios)
  - `version`: App version (optional)
  - `packageName`: App package name or bundle identifier (optional)
  - `description`: Description or notes (optional)
- **Response**: Mobile app file details with ID and URL
- **Status Codes**:
  - 201: Mobile app file uploaded successfully
  - 400: Bad request - missing file or invalid parameters
  - 404: Project not found

### GET `/projects/{projectId}/automation-mobile/upload-mobile-app`
- **Purpose**: Get all mobile app files for a project
- **Response**: Array of mobile app files
- **Status Codes**:
  - 200: List of mobile app files retrieved successfully
  - 404: Project not found

### GET `/projects/{projectId}/automation-mobile/upload-mobile-app/{mobileAppId}`
- **Purpose**: Get a specific mobile app file
- **Parameters**:
  - `mobileAppId`: Unique mobile app file identifier
- **Response**: Mobile app file details
- **Status Codes**:
  - 200: Mobile app file retrieved successfully
  - 404: Mobile app file not found

### DELETE `/projects/{projectId}/automation-mobile/upload-mobile-app/{mobileAppId}`
- **Purpose**: Delete a mobile app file
- **Parameters**:
  - `mobileAppId`: Unique mobile app file identifier
- **Response**: Deletion confirmation
- **Status Codes**:
  - 200: Mobile app file deleted successfully
  - 404: Mobile app file not found
  - 403: Unauthorized to delete this mobile app file

## Database Schema

### New Table: `automation_mobile_test_cases`

```sql
CREATE TABLE automation_mobile_test_cases (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  testCaseId UUID NOT NULL REFERENCES test_cases(id) ON DELETE CASCADE,
  steps JSON NOT NULL,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX IDX_automation_mobile_test_cases_testCaseId
ON automation_mobile_test_cases(testCaseId);
```

### New Table: `automation_mobile_files`

```sql
CREATE TABLE automation_mobile_files (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  testCaseId UUID NOT NULL REFERENCES test_cases(id) ON DELETE CASCADE,
  stepId VARCHAR(50) NOT NULL,
  fileId VARCHAR(255) NOT NULL UNIQUE,
  originalName VARCHAR(255) NOT NULL,
  mimeType VARCHAR(100) NOT NULL,
  fileSize BIGINT NOT NULL DEFAULT 0,
  fileUrl TEXT NOT NULL,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IDX_automation_mobile_files_testCaseId ON automation_mobile_files(testCaseId);
CREATE INDEX IDX_automation_mobile_files_stepId ON automation_mobile_files(stepId);
CREATE UNIQUE INDEX IDX_automation_mobile_files_fileId ON automation_mobile_files(fileId);
```

### New Table: `mobile_app_files`

```sql
CREATE TABLE mobile_app_files (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  projectId UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  originalName VARCHAR(255) NOT NULL,
  mimeType VARCHAR(100) NOT NULL,
  fileSize BIGINT NOT NULL DEFAULT 0,
  fileUrl TEXT NOT NULL,
  platform VARCHAR(20) NOT NULL,
  version VARCHAR(50),
  packageName VARCHAR(255),
  description TEXT,
  uploadedBy VARCHAR(255),
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IDX_mobile_app_files_projectId ON mobile_app_files(projectId);
CREATE INDEX IDX_mobile_app_files_platform ON mobile_app_files(platform);
CREATE INDEX IDX_mobile_app_files_createdAt ON mobile_app_files(createdAt);
```

## Data Transfer Objects (DTOs)

### AutomationMobileTestCaseDto
Main DTO for mobile automation test cases containing:
- `testCaseId`: UUID of the associated test case
- `steps`: Array of `MobileAutomationStep` objects

### MobileAutomationStep
Individual mobile automation step containing:
- `step`: Step number
- `stepName`: Description of the step
- `target`: Mobile element selector (xpath, id, accessibility id, etc.)
- `value`: Value for the action (app package, text input, etc.)
- `action`: Mobile action type (launchApp, tap, swipe, input, assertText, etc.)
- `platform`: Mobile platform (android, ios)
- `orientation`: Device orientation (portrait, landscape)
- `coordinates`: Tap/swipe coordinates in JSON format
- `prompt`: AI-assisted step description
- `Actions`: Multiple actions in JSON format
- `fileUrl`: URL of uploaded files
- `fileId`: Unique file identifier
- Various UI flags for interaction dropdowns

## Mobile-Specific Features

### Mobile Actions
The mobile automation supports various mobile-specific actions:
- `launchApp`: Launch a mobile application
- `tap`: Tap on mobile elements
- `swipe`: Swipe gestures
- `input`: Text input on mobile keyboards
- `assertText`: Assert text content on mobile screens
- And more standard mobile testing actions

### Platform Support
- **Android**: Support for Android-specific selectors and actions
- **iOS**: Support for iOS-specific selectors and actions

### Device Orientation
- **Portrait**: Vertical device orientation
- **Landscape**: Horizontal device orientation

### Coordinate-based Actions
Support for coordinate-based interactions when element selectors are not available.

## File Structure

### New Files Created:
1. `src/test-cases/automation-mobile-test-case.entity.ts` - Entity for mobile automation
2. `src/test-cases/automation-mobile-file.entity.ts` - Entity for mobile automation files
3. `src/test-cases/mobile-app-file.entity.ts` - Entity for mobile app files (APK/IPA)
4. `src/test-cases/dto/automation-mobile-test-case.dto.ts` - DTOs for mobile automation
5. `src/test-cases/dto/upload-automation-mobile-file.dto.ts` - DTO for mobile file uploads
6. `src/test-cases/dto/upload-mobile-app-file.dto.ts` - DTO for mobile app file uploads
7. `src/migrations/1691234567891-CreateAutomationMobileTestCasesTable.ts` - Database migration for mobile automation
8. `src/migrations/1691234567892-CreateAutomationMobileFilesTable.ts` - Database migration for mobile files
9. `src/migrations/1691234567893-CreateMobileAppFilesTable.ts` - Database migration for mobile app files
10. `src/test-cases/test/mobile-automation.test.ts` - Unit tests for mobile automation
11. `src/test-cases/test/mobile-automation-files.test.ts` - Unit tests for mobile file management
12. `src/test-cases/test/mobile-app-files.test.ts` - Unit tests for mobile app file management

### Modified Files:
1. `src/test-cases/test-cases.controller.ts` - Added mobile automation endpoints
2. `src/test-cases/test-cases.service.ts` - Added mobile automation service methods
3. `src/test-cases/test-cases.module.ts` - Added mobile automation entity to module

## Service Methods

### Mobile Automation Steps
#### saveMobileAutomationSteps()
- Creates or updates mobile automation steps for a test case
- Validates project access and test case existence
- Returns the saved mobile automation test case

#### getMobileAutomationSteps()
- Retrieves mobile automation steps for a test case
- Returns null if no mobile automation exists
- Validates project access and test case existence

#### updateMobileAutomationSteps()
- Updates existing mobile automation steps
- Throws NotFoundException if mobile automation doesn't exist
- Validates project access and test case existence

### Mobile Automation File Management
#### uploadMobileAutomationFile()
- Uploads files for mobile automation test case steps
- Stores files in `test-data/automation-mobile-files` folder structure
- Replaces existing files for the same step
- Returns file URL and unique file ID
- Validates project access and test case existence

#### deleteMobileAutomationFile()
- Deletes mobile automation files from both database and storage
- Removes files from Google Cloud Storage
- Validates file ownership and permissions
- Returns deletion confirmation

#### storeMobileFileReference()
- Private method to store mobile file metadata in database
- Creates AutomationMobileFile entity with file details

#### deleteMobileExistingStepFiles()
- Private method to clean up existing mobile files for a step
- Handles both database records and storage files
- Used during file replacement operations

### Mobile App File Management
#### uploadMobileAppFile()
- Uploads mobile app files (APK/IPA) for projects
- Stores files in `test-data/mobile-app-files` folder structure
- Validates file types based on platform (Android APK, iOS IPA)
- Supports file size validation (max 500MB)
- Returns mobile app file entity with metadata

#### getAllMobileAppFiles()
- Retrieves all mobile app files for a project
- Returns files ordered by creation date (newest first)
- Validates project access permissions

#### getMobileAppFile()
- Retrieves a specific mobile app file by ID
- Validates project access and file ownership
- Returns complete file metadata

#### deleteMobileAppFile()
- Deletes mobile app files from both database and storage
- Removes files from Google Cloud Storage
- Validates file ownership and permissions
- Returns deletion confirmation

#### validateMobileAppFile()
- Private method to validate mobile app file types
- Checks file extensions (.apk for Android, .ipa for iOS)
- Validates MIME types and file sizes
- Throws appropriate exceptions for invalid files

## Usage Example

```typescript
// Save mobile automation steps
const mobileAutomationDto = {
  testCaseId: "test-case-uuid",
  steps: [
    {
      step: 1,
      stepName: "Launch mobile app",
      action: "launchApp",
      value: "com.example.app",
      platform: "android",
      orientation: "portrait"
    },
    {
      step: 2,
      stepName: "Tap login button",
      action: "tap",
      target: "//android.widget.Button[@text='Login']",
      platform: "android"
    }
  ]
};

// POST /projects/{projectId}/test-cases/tcId/{tcId}/automation-mobile
```

## File Storage Structure

Mobile automation files are stored with the following path structure:
```
test-data/automation-mobile-files/{projectId}/{testCaseId}/step-{stepId}/{fileId}-{originalFileName}
```

Example:
```
test-data/automation-mobile-files/project123/testcase456/step-1/testcase456-mobile-step-1-1691234567890-screenshot.png
```

This structure separates mobile automation files from web automation files (`test-data/automation-files`) and provides clear organization by project, test case, and step.

### Mobile App Files Storage Structure

Mobile app files (APK/IPA) are stored with the following path structure:
```
test-data/mobile-app-files/{projectId}/{timestamp}-{originalFileName}
```

Example:
```
test-data/mobile-app-files/project123/1691234567890-MyApp-v1.2.3.apk
test-data/mobile-app-files/project123/1691234567891-MyApp-v1.2.3.ipa
```

This structure provides:
- Clear separation from automation step files
- Project-based organization
- Timestamp-based uniqueness to prevent filename conflicts
- Support for both Android APK and iOS IPA files

## Testing

Unit tests have been created to verify:

### Mobile Automation Steps (`src/test-cases/test/mobile-automation.test.ts`)
- Saving mobile automation steps
- Retrieving mobile automation steps
- Updating mobile automation steps
- Error handling for non-existent test cases

### Mobile File Management (`src/test-cases/test/mobile-automation-files.test.ts`)
- Uploading mobile automation files
- Deleting mobile automation files
- File path structure validation
- Error handling for non-existent files
- Storage service integration

### Mobile App File Management (`src/test-cases/test/mobile-app-files.test.ts`)
- Uploading mobile app files (APK/IPA)
- Retrieving all mobile app files for a project
- Retrieving specific mobile app files
- Deleting mobile app files
- File type validation (Android APK, iOS IPA)
- File size validation
- Error handling for invalid files and permissions
- Storage service integration

## Migration

To apply the database changes, run the migration:
```bash
npm run migration:run
```

This will create the new `automation_mobile_test_cases` table with the appropriate foreign key constraints and indexes.
