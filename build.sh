#!/bin/bash

# Load environment variables from .env.production file
if [ -f .env.production ]; then
  export $(grep -v '^#' .env.production | xargs)
fi

# Now build your Docker image, passing the loaded environment variables as build-args
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  -t asia-southeast2-docker.pkg.dev/agentq-464900/agentq/app_backend_agentq:2.3.0 \
  --build-arg NODE_ENV="${NODE_ENV}" \
  --build-arg DB_HOST="${DB_HOST}" \
  --build-arg DB_PORT="${DB_PORT}" \
  --build-arg DB_USERNAME="${DB_USERNAME}" \
  --build-arg DB_PASSWORD="${DB_PASSWORD}" \
  --build-arg DB_DATABASE="${DB_DATABASE}" \
  --build-arg JWT_SECRET="${JWT_SECRET}" \
  --build-arg ENABLE_CLOUD_STORAGE="${ENABLE_CLOUD_STORAGE}" \
  --build-arg GCP_PROJECT_ID="${GCP_PROJECT_ID}" \
  --build-arg GCP_CLIENT_EMAIL="${GCP_CLIENT_EMAIL}" \
  --build-arg GCP_PRIVATE_KEY="${GCP_PRIVATE_KEY}" \
  --build-arg GCP_BUCKET_NAME="${GCP_BUCKET_NAME}" \
  --push .



# docker buildx build \
#   --platform linux/amd64,linux/arm64 \
#   --build-arg NODE_ENV="${NODE_ENV}" \
#   --build-arg DB_HOST="${DB_HOST}" \
#   --build-arg DB_PORT="${DB_PORT}" \
#   --build-arg DB_USERNAME="${DB_USERNAME}" \
#   --build-arg DB_PASSWORD="${DB_PASSWORD}" \
#   --build-arg DB_DATABASE="${DB_DATABASE}" \
#   --build-arg JWT_SECRET="${JWT_SECRET}" \
#   --build-arg ENABLE_CLOUD_STORAGE="${ENABLE_CLOUD_STORAGE}" \
#   --build-arg GCP_PROJECT_ID="${GCP_PROJECT_ID}" \
#   --build-arg GCP_CLIENT_EMAIL="${GCP_CLIENT_EMAIL}" \
#   --build-arg GCP_PRIVATE_KEY="${GCP_PRIVATE_KEY}" \
#   --build-arg GCP_BUCKET_NAME="${GCP_BUCKET_NAME}" \
#   -t app_backend_agentq .