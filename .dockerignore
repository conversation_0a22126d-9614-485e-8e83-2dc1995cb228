# Node modules and build output
node_modules
dist

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage and test output
coverage
*.lcov
.nyc_output
test
tests

# TypeScript cache
*.tsbuildinfo

# dotenv environment variable files
.env
!.env.example

# Misc
.DS_Store
.cache
.parcel-cache
bower_components
jspm_packages
web_modules
build/
lib-cov
.grunt
.next
out
.nuxt
.vuepress
.temp
.docusaurus
.serverless
.fusebox
.dynamodb
.tern-port
.vscode-test
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
