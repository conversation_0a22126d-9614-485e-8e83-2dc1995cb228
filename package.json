{"name": "app_backend_agentq", "version": "1.0.0", "description": "AgentQ Backend Microservice", "private": true, "main": "dist/main.ts", "scripts": {"prebuild": "rm -rf dist && rm -rf tsconfig.tsbuildinfo", "build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main"}, "dependencies": {"@google-cloud/storage": "^7.15.2", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^10.1.1", "@nestjs/passport": "^10.0.2", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.4.2", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^10.0.0", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "csv-parse": "^5.6.0", "openai": "^4.86.2", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.17", "zod": "^3.24.2"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@types/bcrypt": "^5.0.1", "@types/express": "^4.17.17", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.11", "@types/passport-local": "^1.0.37", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "prettier": "^3.0.0", "rimraf": "^6.0.1", "source-map-support": "^0.5.21", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.3"}}