# ---- Builder Stage ----
FROM node:22-alpine AS builder

WORKDIR /app

# Install dependencies (including devDependencies)
COPY package*.json ./
RUN npm install

# Copy source files
COPY . .

# Build the app (NestJS -> dist/)
RUN npm run build

# ---- Production Stage ----
FROM node:22 AS production

WORKDIR /app

ARG NODE_ENV
ARG DB_HOST
ARG DB_PORT
ARG DB_USERNAME
ARG DB_PASSWORD
ARG DB_DATABASE
ARG JWT_SECRET
ARG ENABLE_CLOUD_STORAGE
ARG GCP_PROJECT_ID
ARG GCP_CLIENT_EMAIL
ARG GCP_PRIVATE_KEY
ARG GCP_BUCKET_NAME

ENV NODE_ENV=${NODE_ENV}
ENV DB_HOST=${DB_HOST}
ENV DB_PORT=${DB_PORT}
ENV DB_USERNAME=${DB_USERNAME}
ENV DB_PASSWORD=${DB_PASSWORD}
ENV DB_DATABASE=${DB_DATABASE}
ENV JWT_SECRET=${JWT_SECRET}
ENV ENABLE_CLOUD_STORAGE=${ENABLE_CLOUD_STORAGE}
ENV GCP_PROJECT_ID=${GCP_PROJECT_ID}
ENV GCP_CLIENT_EMAIL=${GCP_CLIENT_EMAIL}
ENV GCP_PRIVATE_KEY=${GCP_PRIVATE_KEY}
ENV GCP_BUCKET_NAME=${GCP_BUCKET_NAME}

# Install system dependencies for crypto and GCS
RUN apt-get update && apt-get install -y openssl ca-certificates && rm -rf /var/lib/apt/lists/*

# Install only production dependencies
COPY package*.json ./
RUN npm install --omit=dev

# Copy built files from builder
COPY --from=builder /app/dist ./dist

# If you need static assets, copy them here (uncomment if needed)
# COPY --from=builder /app/public ./public

EXPOSE 3010

CMD ["node", "dist/main.js"]
