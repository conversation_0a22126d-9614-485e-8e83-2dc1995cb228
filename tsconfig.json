{
  "compilerOptions": {
    "target": "ES2020", // Specify ECMAScript target version
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "module": "CommonJS", // Specify module code generation
    "strict": true, // Enable all strict type-checking options
    "esModuleInterop": true, // Enable compatibility with CommonJS and ES Modules
    "skipLibCheck": true, // Skip type checking of declaration files
    "outDir": "./dist", // Redirect output structure to the 'dist' folder
    "rootDir": "./src", // Specify the root folder of your source files
    // "strictPropertyInitialization": false,
    "declaration": true,
    "removeComments": true,
    "allowSyntheticDefaultImports": true,
    "sourceMap": true,
    "incremental": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false
  },
  "include": ["src"], // Include all files in the 'src' folder
  "exclude": ["node_modules", "dist"] // Exclude 'node_modules' and 'dist' folders
}