import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddVulnerabilityCountFields1721000000005 implements MigrationInterface {
  name = 'AddVulnerabilityCountFields1721000000005';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "test_results" 
      ADD COLUMN "highCount" integer,
      ADD COLUMN "mediumCount" integer,
      ADD COLUMN "lowCount" integer,
      ADD COLUMN "informationalCount" integer,
      ADD COLUMN "falsePositiveCount" integer
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "test_results" 
      DROP COLUMN "highCount",
      DROP COLUMN "mediumCount",
      DROP COLUMN "lowCount",
      DROP COLUMN "informationalCount",
      DROP COLUMN "falsePositiveCount"
    `);
  }
}
