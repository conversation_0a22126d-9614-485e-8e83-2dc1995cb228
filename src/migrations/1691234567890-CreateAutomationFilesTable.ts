// import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

// export class CreateAutomationFilesTable1691234567890 implements MigrationInterface {
//   public async up(queryRunner: QueryRunner): Promise<void> {
//     await queryRunner.createTable(
//       new Table({
//         name: 'automation_files',
//         columns: [
//           {
//             name: 'id',
//             type: 'uuid',
//             isPrimary: true,
//             generationStrategy: 'uuid',
//             default: 'uuid_generate_v4()',
//           },
//           {
//             name: 'testCaseId',
//             type: 'uuid',
//             isNullable: false,
//           },
//           {
//             name: 'stepId',
//             type: 'varchar',
//             length: '50',
//             isNullable: false,
//           },
//           {
//             name: 'fileId',
//             type: 'varchar',
//             length: '255',
//             isNullable: false,
//           },
//           {
//             name: 'originalName',
//             type: 'varchar',
//             length: '255',
//             isNullable: false,
//           },
//           {
//             name: 'mimeType',
//             type: 'varchar',
//             length: '100',
//             isNullable: false,
//           },
//           {
//             name: 'fileSize',
//             type: 'bigint',
//             isNullable: false,
//             default: 0,
//           },
//           {
//             name: 'fileUrl',
//             type: 'text',
//             isNullable: false,
//           },
//           {
//             name: 'createdAt',
//             type: 'timestamp',
//             default: 'CURRENT_TIMESTAMP',
//           },
//           {
//             name: 'updatedAt',
//             type: 'timestamp',
//             default: 'CURRENT_TIMESTAMP',
//             onUpdate: 'CURRENT_TIMESTAMP',
//           },
//         ],
//         indices: [
//           {
//             name: 'IDX_automation_files_testCaseId',
//             columnNames: ['testCaseId'],
//           },
//           {
//             name: 'IDX_automation_files_stepId',
//             columnNames: ['stepId'],
//           },
//           {
//             name: 'IDX_automation_files_fileId',
//             columnNames: ['fileId'],
//             isUnique: true,
//           },
//         ],
//       }),
//       true,
//     );

//     // Add foreign key constraint
//     await queryRunner.createForeignKey(
//       'automation_files',
//       new TableForeignKey({
//         columnNames: ['testCaseId'],
//         referencedColumnNames: ['id'],
//         referencedTableName: 'test_cases',
//         onDelete: 'CASCADE',
//       }),
//     );
//   }

//   public async down(queryRunner: QueryRunner): Promise<void> {
//     await queryRunner.dropTable('automation_files');
//   }
// }
