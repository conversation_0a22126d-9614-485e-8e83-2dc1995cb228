import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddLogsSecurityUrlToTestResults1721000000002 implements MigrationInterface {
  name = 'AddLogsSecurityUrlToTestResults1721000000002';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'test_results',
      new TableColumn({
        name: 'logsSecurityUrl',
        type: 'varchar',
        length: '500',
        isNullable: true,
        comment: 'Google Cloud Storage URL for ZAP security report (DAST)'
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('test_results', 'logsSecurityUrl');
  }
}
