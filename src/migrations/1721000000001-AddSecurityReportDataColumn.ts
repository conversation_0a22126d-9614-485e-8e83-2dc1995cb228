import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddSecurityReportDataColumn1721000000001 implements MigrationInterface {
  name = 'AddSecurityReportDataColumn1721000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'temp_test_results',
      new TableColumn({
        name: 'securityReportData',
        type: 'jsonb',
        isNullable: true,
        comment: 'Detailed ZAP security report data including summary and vulnerabilities'
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('temp_test_results', 'securityReportData');
  }
}
