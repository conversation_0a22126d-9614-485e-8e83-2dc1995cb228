import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

export class CreateAutomationMobileTestCasesTable1691234567891 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'automation_mobile_test_cases',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'testCaseId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'steps',
            type: 'json',
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
        indices: [
          {
            name: 'IDX_automation_mobile_test_cases_testCaseId',
            columnNames: ['testCaseId'],
            isUnique: true,
          },
        ],
      }),
      true,
    );

    // Add foreign key constraint
    await queryRunner.createForeignKey(
      'automation_mobile_test_cases',
      new TableForeignKey({
        columnNames: ['testCaseId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'test_cases',
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('automation_mobile_test_cases');
  }
}
