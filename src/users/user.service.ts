import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from './user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { CompaniesService } from '../companies/companies.service';
import * as crypto from 'crypto';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private companiesService: CompaniesService,
  ) {}

  async findOne(email: string): Promise<User | undefined> {
    const user = await this.usersRepository.findOne({ where: { email } });
    return user || undefined;
  }

  async findById(id: string): Promise<User | undefined> {
    console.log('Finding user by ID:', id);
    const user = (await this.usersRepository.findOne({ where: { id } })) || undefined;
    if (user) {
      console.log('User found:', user.email);
    } else {
      console.log('No user found with ID:', id);
    }
    return user;
  }

  async create(createUserDto: CreateUserDto): Promise<User> {
    const { email, password, name, companyId } = createUserDto;

    // Check if user already exists
    const existingUser = await this.findOne(createUserDto.email ?? '');
    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // Hash password
    const salt = await bcrypt.genSalt();
    const hashedPassword = await bcrypt.hash(password ?? '', salt);

    // Create new user
    const user = this.usersRepository.create({
      email,
      password: hashedPassword,
      name,
      companyId
    });

    const savedUser = await this.usersRepository.save(user);
    console.log('Created user with ID:', savedUser.id);
    return savedUser;
  }

  async getProfile(userId: string): Promise<User> {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }
  
  async updateCompany(userId: string, companyId: string): Promise<User> {
    const user = await this.usersRepository.findOne({ where: { id: userId } });
    
    if (!user) {
      throw new Error(`User not found with ID: ${userId}`);
    }
    
    user.companyId = companyId;
    return this.usersRepository.save(user);
  }


  async findOrCreateFromJwt(userData: {
    id: string;
    email: string;
    role?: string;
    companyId?: string;
  }): Promise<User> {
    console.log('Finding or creating user from JWT data:', userData);
    
    // First try to find by ID
    let user = await this.findById(userData.id);
    
    // If not found by ID, try by email
    if (!user) {
      user = await this.findOne(userData.email);
    }
    
    // If user exists, return it
    if (user) {
      console.log('User already exists:', user.email);
      return user;
    }
    
    // If no companyId provided, we need to create a company
    if (!userData.companyId) {
      // Generate a UUID for the company ID instead of passing undefined
      const companyId = crypto.randomUUID();
      const companyName = `${userData.email.split('@')[0]}'s Company`;
      const company = await this.companiesService.registerCompany(
        companyId,
        companyName,
        userData.id
      );
      userData.companyId = company.id;
    }
    
    // Create a new user with the JWT data
    const createUserDto: CreateUserDto = {
      id: userData.id,
      email: userData.email,
      name: userData.email.split('@')[0],
      password: '', // Empty password for users created from JWT
      companyId: userData.companyId
    };
    
    // Only add role if it's provided
    if (userData.role) {
      createUserDto.role = userData.role;
    }
    
    const newUser = this.usersRepository.create(createUserDto);
    
    // Set role directly on the entity if needed
    if (!newUser.role) {
      newUser.role = 'user';
    }
    
    console.log('Saving new user:', newUser.email);
    return this.usersRepository.save(newUser);
  }

  async createFromExternalAuth(userData: { id: string; email: string; name: string; role?: string; companyId?: string }) {
    // Check if user already exists
    let user = await this.findOne(userData.email);
    
    if (!user) {
      // Create new user based on external auth data
      const createUserDto: CreateUserDto = {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        password: '', // Empty password for users created from external auth
        role: userData.role || 'user',
        companyId: userData.companyId
      };
      
      // Check if company exists, if not create it
      if (userData.companyId) {
        const company = await this.companiesService.findById(userData.companyId);
        if (!company) {
          // Create company from the data in the token
          await this.companiesService.registerCompany(
            userData.companyId,
            `${userData.name}'s Company`,
            userData.id
          );
        }
      }
      
      user = this.usersRepository.create(createUserDto);
      await this.usersRepository.save(user);
    }
    
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      companyId: user.companyId
    };
  }

  async removeByEmail(email: string): Promise<void> {
    const user = await this.findOne(email);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    await this.usersRepository.remove(user);
  }
}
