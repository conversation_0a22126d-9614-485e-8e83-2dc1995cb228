import { Controller, Get, Post, Param, Query, Request, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';
import { IssueResponseDto } from '../test-results/dto/issues.dto';
import { TestRunsService } from '../test-runs/test-runs.service';
import { JiraService } from '../integrations/jira.service';

@ApiTags('issues')
@Controller('projects/:projectId/issues')
@UseGuards(JwtAuthGuard)
export class IssuesController {
  constructor(
    private readonly testRunsService: TestRunsService,
    private readonly jiraService: JiraService
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all issues for a project' })
  @ApiResponse({ status: 200, description: 'List of issues', type: [IssueResponseDto] })
  async getProjectIssues(
    @Param('projectId') projectId: string,
    @Request() req: any,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Query('search') search?: string,
    @Query('sortField') sortField: string = 'createdAt',
    @Query('sortDirection') sortDirection: string = 'desc',
    @Query('status') status?: string | string[],
    @Query('testRunId') testRunId?: string | string[],
    @Query('createdAfter') createdAfter?: string,
    @Query('createdBefore') createdBefore?: string
  ) {
    // Convert array-like query parameters to arrays
    const statusArray = Array.isArray(status) ? status : status ? [status] : [];
    const testRunIdArray = Array.isArray(testRunId) ? testRunId : testRunId ? [testRunId] : [];

    return this.testRunsService.getProjectIssues(projectId, req.user.id, {
      page: +page,
      limit: +limit,
      search,
      sortField,
      sortDirection: sortDirection === 'asc' ? 'ASC' : 'DESC',
      filters: {
        status: statusArray,
        testRunId: testRunIdArray,
        createdAfter,
        createdBefore
      }
    });
  }

  @Get('/debug')
  @ApiOperation({ summary: 'Debug user and project access for issues' })
  @ApiResponse({ status: 200, description: 'Debug information' })
  async debugUserAccess(
    @Param('projectId') projectId: string,
    @Request() req: any
  ) {
    try {
      const user = req.user;
      console.log('🐛 Debug endpoint - User from JWT:', user);

      // Call the same method that's used in the regular endpoint
      const result = await this.testRunsService.getProjectIssues(projectId, user.id, {
        page: 1,
        limit: 100,
        sortField: 'createdAt',
        sortDirection: 'DESC'
      });

      return {
        user: {
          id: user.id,
          email: user.email,
          companyId: user.companyId
        },
        projectId: projectId,
        issuesFound: result.issues.length,
        totalIssues: result.total,
        issues: result.issues
      };
    } catch (error: any) {
      console.error('🐛 Debug endpoint error:', error);
      return {
        error: error?.message || 'Unknown error',
        stack: error?.stack || 'No stack trace'
      };
    }
  }

  @Post('/sync')
  @ApiOperation({ summary: 'Sync issues with JIRA' })
  @ApiResponse({ status: 200, description: 'Issues synced successfully' })
  async syncWithJira(
    @Param('projectId') projectId: string,
    @Request() req: any
  ) {
    // We're using the JiraService in the controller to demonstrate that it's properly injected
    // The actual sync logic is in the TestRunsService
    try {
      // Try to get projects as a simple check if JIRA is configured
      const projects = await this.jiraService.getProjects(req.user.companyId);
      if (projects.length === 0) {
        return {
          message: 'JIRA integration is not configured or no projects found',
          updated: 0,
          deleted: 0
        };
      }

      return this.testRunsService.syncIssuesWithJira(projectId, req.user.id);
    } catch (error) {
      return {
        message: 'Error checking JIRA integration: ' + (error || 'Unknown error'),
        updated: 0,
        deleted: 0
      };
    }
  }
}
