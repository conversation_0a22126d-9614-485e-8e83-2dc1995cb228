import { Controller, Post, Body, Request, UseGuards, Req, HttpException, HttpStatus } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';
import { CompaniesService } from './companies.service';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiBody } from '@nestjs/swagger';

interface HttpError extends Error {
  status?: number;
}

@ApiTags('companies')
@Controller('companies')
export class CompaniesController {
  constructor(
    private readonly companiesService: CompaniesService,
  ) {}

  @UseGuards(JwtAuthGuard)
  @Post('register')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Register a company' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: '704f4925-9bd3-46b4-8bce-28f8671ef482' },
        name: { type: 'string', example: 'Example Company' },
        userId: { type: 'string', example: 'aca4c5be-2b12-4e9e-b6e6-f96638d8e1d6' },
        subscription: { 
          type: 'object', 
          properties: {
            name: { type: 'string', example: 'Enterprise' },
            isEnterprise: { type: 'boolean', example: true }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 201, description: 'Company registered successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Valid JWT token required' })
  async registerCompany(
    @Body() companyData: { id: string; name: string; userId: string; companyId?: string; subscription?: any },
    @Request() req
  ) {
    try {
      console.log('Register company request:', JSON.stringify(companyData));
      console.log('Authenticated user:', req.user ? JSON.stringify(req.user) : 'No user');
      
      // Check if company exists
      let company = await this.companiesService.findById(companyData.id);
      
      if (!company) {
        // Create the company using registerCompany method
        company = await this.companiesService.registerCompany(
          companyData.id,
          companyData.name,
          companyData.userId || (req.user ? req.user.id : null)
        );
        
        // Update user with company ID if needed
        if (req.user && !req.user.companyId) {
          await this.companiesService.updateUserCompany(req.user.id, company.id);
        } else if (companyData.userId) {
          await this.companiesService.updateUserCompany(companyData.userId, company.id);
        }
      }
      
      return company;
    } catch (error) {
      console.error('Error registering company:', error);
      throw error;
    }
  }
}
