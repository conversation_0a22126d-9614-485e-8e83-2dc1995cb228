import { <PERSON><PERSON>ty, <PERSON>umn, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { User } from '../../users/user.entity';
import { Project } from '../../projects/project.entity';

@Entity('companies')
export class Company {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  company_name: string;

  @Column({ unique: true })
  token: string;

  @Column({ nullable: true })
  userId: string;

  @OneToMany(() => User, user => user.company)
  users: User[];

  @OneToMany(() => Project, project => project.company)
  projects: Project[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}