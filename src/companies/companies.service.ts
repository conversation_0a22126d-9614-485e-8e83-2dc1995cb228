import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Company } from './entities/company.entity';
import { User } from '../users/user.entity';
import * as crypto from 'crypto';

@Injectable()
export class CompaniesService {
  constructor(
    @InjectRepository(Company)
    private companiesRepository: Repository<Company>,
    
    @InjectRepository(User)
    private usersRepository: Repository<User>
  ) {}

  // Helper method to generate a unique token for the company
  private generateCompanyToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  async registerCompany(companyId: string, companyName: string, userId: string) {
    console.log(`Registering company: ${companyName} (${companyId}) for user ${userId}`);
    
    // Check if company already exists
    let company = await this.companiesRepository.findOne({ where: { id: companyId } });
    
    if (!company) {
      // Create new company with a token
      company = this.companiesRepository.create({
        id: companyId,
        company_name: companyName,
        token: this.generateCompanyToken(), // Generate a token
        userId: userId // Set the userId
      });
      
      company = await this.companiesRepository.save(company);
      console.log(`Created new company with ID: ${company.id}`);
    } else {
      console.log(`Company already exists: ${company.id}`);
    }
    
    return company;
  }

  async updateUserCompany(userId: string, companyId: string) {
    console.log(`Updating user ${userId} with company ${companyId}`);
    
    const user = await this.usersRepository.findOne({ where: { id: userId } });
    
    if (!user) {
      throw new NotFoundException(`User not found with ID: ${userId}`);
    }
    
    user.companyId = companyId;
    await this.usersRepository.save(user);
    
    console.log(`Updated user ${userId} with company ${companyId}`);
    
    return user;
  }

  async findById(id: string) {
    return this.companiesRepository.findOne({ where: { id } });
  }
}
