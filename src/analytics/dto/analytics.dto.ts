import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsOptional, IsDateString, IsEnum } from 'class-validator';

export class ProjectAnalyticsParamsDto {
  @ApiProperty({
    description: 'Project UUID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  projectId: string;
}

export class AnalyticsQueryDto {
  @ApiProperty({
    description: 'Start date for analytics data (ISO string)',
    example: '2024-01-01T00:00:00.000Z',
    required: false
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    description: 'End date for analytics data (ISO string)',
    example: '2024-12-31T23:59:59.999Z',
    required: false
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;
}

export enum AnalyticsTimeRange {
  LAST_7_DAYS = 'last_7_days',
  LAST_30_DAYS = 'last_30_days',
  LAST_3_MONTHS = 'last_3_months',
  LAST_6_MONTHS = 'last_6_months',
  LAST_YEAR = 'last_year'
}

export class AnalyticsTimeRangeDto {
  @ApiProperty({
    description: 'Predefined time range for analytics',
    enum: AnalyticsTimeRange,
    example: AnalyticsTimeRange.LAST_30_DAYS,
    required: false
  })
  @IsOptional()
  @IsEnum(AnalyticsTimeRange)
  timeRange?: AnalyticsTimeRange;
}

// Response DTOs for better API documentation
export class AutomationCoverageDto {
  @ApiProperty({ example: 'automation' })
  type: string;

  @ApiProperty({ example: '25' })
  count: string;
}

export class TestCaseDistributionDto {
  @ApiProperty({ example: 'functional' })
  type: string;

  @ApiProperty({ example: '15' })
  count: string;
}

export class PriorityDistributionDto {
  @ApiProperty({ example: 'high' })
  priority: string;

  @ApiProperty({ example: '10' })
  count: string;
}

export class PlatformDistributionDto {
  @ApiProperty({ example: 'web' })
  platform: string;

  @ApiProperty({ example: '20' })
  count: string;
}

export class GrowthOverTimeDto {
  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  month: string;

  @ApiProperty({ example: '5' })
  count: string;
}

export class TestCaseAnalyticsResponseDto {
  @ApiProperty({ type: [AutomationCoverageDto] })
  automationCoverage: AutomationCoverageDto[];

  @ApiProperty({ type: [TestCaseDistributionDto] })
  typeDistribution: TestCaseDistributionDto[];

  @ApiProperty({ type: [PriorityDistributionDto] })
  priorityDistribution: PriorityDistributionDto[];

  @ApiProperty({ type: [PlatformDistributionDto] })
  platformDistribution: PlatformDistributionDto[];

  @ApiProperty({ type: [GrowthOverTimeDto] })
  growthOverTime: GrowthOverTimeDto[];
}

export class PassFailDataDto {
  @ApiProperty()
  testRunId: string;

  @ApiProperty()
  testRunName: string;

  @ApiProperty()
  createdAt: string;

  @ApiProperty({ example: 'passed' })
  status: string;

  @ApiProperty({ example: '15' })
  count: string;
}

export class ExecutionTimeDto {
  @ApiProperty()
  testRunId: string;

  @ApiProperty()
  testRunName: string;

  @ApiProperty({ example: '45.5' })
  avgDuration: string;

  @ApiProperty({ example: '120.0' })
  maxDuration: string;

  @ApiProperty({ example: '10.2' })
  minDuration: string;
}

export class FlakyTestDto {
  @ApiProperty()
  testCaseId: string;

  @ApiProperty()
  testCaseTitle: string;

  @ApiProperty({ example: '2' })
  statusCount: string;

  @ApiProperty({ example: '5' })
  totalRuns: string;
}

export class TestRunPerformanceResponseDto {
  @ApiProperty({ type: [PassFailDataDto] })
  passFail: PassFailDataDto[];

  @ApiProperty({ type: [ExecutionTimeDto] })
  executionTime: ExecutionTimeDto[];

  @ApiProperty({ type: [FlakyTestDto] })
  flakyTests: FlakyTestDto[];
}

export class ExecutionFrequencyDto {
  @ApiProperty()
  testCaseId: string;

  @ApiProperty()
  testCaseTitle: string;

  @ApiProperty()
  testCaseType: string;

  @ApiProperty({ example: '15' })
  executionCount: string;
}

export class ExecutionEfficiencyResponseDto {
  @ApiProperty({ type: [AutomationCoverageDto] })
  automationStats: AutomationCoverageDto[];

  @ApiProperty({ type: [ExecutionFrequencyDto] })
  executionFrequency: ExecutionFrequencyDto[];
}

export class ProjectCoverageDto {
  @ApiProperty()
  projectId: string;

  @ApiProperty()
  projectName: string;

  @ApiProperty({ example: '100' })
  totalTestCases: string;

  @ApiProperty({ example: '75' })
  automatedTestCases: string;
}

export class ProjectPassRateDto {
  @ApiProperty()
  projectId: string;

  @ApiProperty()
  projectName: string;

  @ApiProperty({ example: '200' })
  totalTests: string;

  @ApiProperty({ example: '180' })
  passedTests: string;
}

export class QualityComparisonResponseDto {
  @ApiProperty({ type: [ProjectCoverageDto] })
  projectCoverage: ProjectCoverageDto[];

  @ApiProperty({ type: [ProjectPassRateDto] })
  projectPassRates: ProjectPassRateDto[];
}

export class TestingVelocityDto {
  @ApiProperty()
  projectId: string;

  @ApiProperty()
  projectName: string;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  month: string;

  @ApiProperty({ example: '25' })
  testRunCount: string;
}

export class QualityTrendDto {
  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  week: string;

  @ApiProperty({ example: '500' })
  totalTests: string;

  @ApiProperty({ example: '450' })
  passedTests: string;
}

export class TestingHealthResponseDto {
  @ApiProperty({ type: [AutomationCoverageDto] })
  overallAutomation: AutomationCoverageDto[];

  @ApiProperty({ type: [TestingVelocityDto] })
  testingVelocity: TestingVelocityDto[];

  @ApiProperty({ type: [QualityTrendDto] })
  qualityTrends: QualityTrendDto[];
}

export class AITestCaseDto {
  @ApiProperty()
  isAIGenerated: boolean;

  @ApiProperty({ example: '45' })
  count: string;
}

export class AIResultDto {
  @ApiProperty()
  isAIGenerated: boolean;

  @ApiProperty({ example: 'passed' })
  status: string;

  @ApiProperty({ example: '38' })
  count: string;
}

export class AITestingImpactResponseDto {
  @ApiProperty({ type: [AITestCaseDto] })
  aiTestCases: AITestCaseDto[];

  @ApiProperty({ type: [AIResultDto] })
  aiVsManualResults: AIResultDto[];
}
