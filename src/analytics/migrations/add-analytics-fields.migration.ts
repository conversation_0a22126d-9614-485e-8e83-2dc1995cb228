import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAnalyticsFields1234567890123 implements MigrationInterface {
  name = 'AddAnalyticsFields1234567890123';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add automationByAgentq field to test_cases table if it doesn't exist
    const testCasesTable = await queryRunner.getTable('test_cases');
    const automationByAgentqColumn = testCasesTable?.findColumnByName('automationByAgentq');
    
    if (!automationByAgentqColumn) {
      await queryRunner.query(`
        ALTER TABLE "test_cases" 
        ADD COLUMN "automationByAgentq" boolean DEFAULT false
      `);
    }

    // Add duration field to test_results table if it doesn't exist
    const testResultsTable = await queryRunner.getTable('test_results');
    const durationColumn = testResultsTable?.findColumnByName('duration');
    
    if (!durationColumn) {
      await queryRunner.query(`
        ALTER TABLE "test_results" 
        ADD COLUMN "duration" integer
      `);
    }

    // Add isLatest field to test_results table if it doesn't exist
    const isLatestColumn = testResultsTable?.findColumnByName('isLatest');
    
    if (!isLatestColumn) {
      await queryRunner.query(`
        ALTER TABLE "test_results" 
        ADD COLUMN "isLatest" boolean DEFAULT true
      `);
    }

    // Create indexes for better query performance
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_test_cases_project_id_created_at" 
      ON "test_cases" ("projectId", "createdAt")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_test_cases_project_id_test_case_type" 
      ON "test_cases" ("projectId", "testCaseType")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_test_cases_automation_by_agentq" 
      ON "test_cases" ("automationByAgentq")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_test_results_test_run_id_is_latest" 
      ON "test_results" ("testRunId", "isLatest")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_test_results_created_at_is_latest" 
      ON "test_results" ("createdAt", "isLatest")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_test_results_status_is_latest" 
      ON "test_results" ("status", "isLatest")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_test_runs_project_id_created_at" 
      ON "test_runs" ("projectId", "createdAt")
    `);

    // Update existing test results to set isLatest = true for the most recent result per test case per test run
    await queryRunner.query(`
      UPDATE "test_results" 
      SET "isLatest" = false 
      WHERE "isLatest" = true
    `);

    await queryRunner.query(`
      UPDATE "test_results" 
      SET "isLatest" = true 
      WHERE "id" IN (
        SELECT DISTINCT ON ("testCaseId", "testRunId") "id"
        FROM "test_results"
        ORDER BY "testCaseId", "testRunId", "createdAt" DESC
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_test_cases_project_id_created_at"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_test_cases_project_id_test_case_type"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_test_cases_automation_by_agentq"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_test_results_test_run_id_is_latest"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_test_results_created_at_is_latest"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_test_results_status_is_latest"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_test_runs_project_id_created_at"`);

    // Remove columns (optional - be careful with data loss)
    // await queryRunner.query(`ALTER TABLE "test_cases" DROP COLUMN "automationByAgentq"`);
    // await queryRunner.query(`ALTER TABLE "test_results" DROP COLUMN "duration"`);
    // await queryRunner.query(`ALTER TABLE "test_results" DROP COLUMN "isLatest"`);
  }
}
