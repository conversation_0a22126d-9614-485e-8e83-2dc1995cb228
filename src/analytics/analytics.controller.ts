import { <PERSON>, Get, Param, UseG<PERSON>s, Request, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';
import { AnalyticsService } from './analytics.service';

@ApiTags('analytics')
@Controller('analytics')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  // Project-specific analytics endpoints
  @Get('projects/:projectId/test-cases')
  @ApiOperation({ summary: 'Get test case analytics for a specific project' })
  @ApiParam({ name: 'projectId', description: 'Project UUID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Test case analytics data',
    schema: {
      type: 'object',
      properties: {
        automationCoverage: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: { type: 'string', example: 'automation' },
              count: { type: 'string', example: '25' }
            }
          }
        },
        typeDistribution: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: { type: 'string', example: 'functional' },
              count: { type: 'string', example: '15' }
            }
          }
        },
        priorityDistribution: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              priority: { type: 'string', example: 'high' },
              count: { type: 'string', example: '10' }
            }
          }
        },
        platformDistribution: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              platform: { type: 'string', example: 'web' },
              count: { type: 'string', example: '20' }
            }
          }
        },
        growthOverTime: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              month: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
              count: { type: 'string', example: '5' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Project not found or access denied' })
  async getProjectTestCaseAnalytics(
    @Param('projectId', ParseUUIDPipe) projectId: string,
    @Request() req
  ) {
    return this.analyticsService.getProjectTestCaseAnalytics(projectId, req.user.id);
  }

  @Get('projects/:projectId/test-runs')
  @ApiOperation({ summary: 'Get test run performance analytics for a specific project' })
  @ApiParam({ name: 'projectId', description: 'Project UUID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Test run performance data',
    schema: {
      type: 'object',
      properties: {
        passFail: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              testRunId: { type: 'string' },
              testRunName: { type: 'string' },
              createdAt: { type: 'string' },
              status: { type: 'string', example: 'passed' },
              count: { type: 'string', example: '15' }
            }
          }
        },
        executionTime: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              testRunId: { type: 'string' },
              testRunName: { type: 'string' },
              avgDuration: { type: 'string', example: '45.5' },
              maxDuration: { type: 'string', example: '120.0' },
              minDuration: { type: 'string', example: '10.2' }
            }
          }
        },
        flakyTests: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              testCaseId: { type: 'string' },
              testCaseTitle: { type: 'string' },
              statusCount: { type: 'string', example: '2' },
              totalRuns: { type: 'string', example: '5' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Project not found or access denied' })
  async getProjectTestRunPerformance(
    @Param('projectId', ParseUUIDPipe) projectId: string,
    @Request() req
  ) {
    return this.analyticsService.getProjectTestRunPerformance(projectId, req.user.id);
  }

  @Get('projects/:projectId/execution-efficiency')
  @ApiOperation({ summary: 'Get test execution efficiency analytics for a specific project' })
  @ApiParam({ name: 'projectId', description: 'Project UUID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Test execution efficiency data',
    schema: {
      type: 'object',
      properties: {
        automationStats: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: { type: 'string', example: 'automation' },
              count: { type: 'string', example: '25' }
            }
          }
        },
        executionFrequency: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              testCaseId: { type: 'string' },
              testCaseTitle: { type: 'string' },
              testCaseType: { type: 'string' },
              executionCount: { type: 'string', example: '15' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Project not found or access denied' })
  async getProjectExecutionEfficiency(
    @Param('projectId', ParseUUIDPipe) projectId: string,
    @Request() req
  ) {
    return this.analyticsService.getProjectExecutionEfficiency(projectId, req.user.id);
  }

  // Cross-project analytics endpoints
  @Get('cross-project/quality-comparison')
  @ApiOperation({ summary: 'Get quality comparison across all accessible projects' })
  @ApiResponse({ 
    status: 200, 
    description: 'Cross-project quality comparison data',
    schema: {
      type: 'object',
      properties: {
        projectCoverage: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              projectId: { type: 'string' },
              projectName: { type: 'string' },
              totalTestCases: { type: 'string', example: '100' },
              automatedTestCases: { type: 'string', example: '75' }
            }
          }
        },
        projectPassRates: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              projectId: { type: 'string' },
              projectName: { type: 'string' },
              totalTests: { type: 'string', example: '200' },
              passedTests: { type: 'string', example: '180' }
            }
          }
        }
      }
    }
  })
  async getCrossProjectQualityComparison(@Request() req) {
    return this.analyticsService.getCrossProjectQualityComparison(req.user.id);
  }

  @Get('organization/testing-health')
  @ApiOperation({ summary: 'Get organization-wide testing health metrics' })
  @ApiResponse({ 
    status: 200, 
    description: 'Organization testing health data',
    schema: {
      type: 'object',
      properties: {
        overallAutomation: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: { type: 'string', example: 'automation' },
              count: { type: 'string', example: '150' }
            }
          }
        },
        testingVelocity: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              projectId: { type: 'string' },
              projectName: { type: 'string' },
              month: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
              testRunCount: { type: 'string', example: '25' }
            }
          }
        },
        qualityTrends: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              week: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
              totalTests: { type: 'string', example: '500' },
              passedTests: { type: 'string', example: '450' }
            }
          }
        }
      }
    }
  })
  async getOrganizationTestingHealth(@Request() req) {
    return this.analyticsService.getOrganizationTestingHealth(req.user.id);
  }

  @Get('ai-testing/impact')
  @ApiOperation({ summary: 'Get AI testing impact analytics' })
  @ApiResponse({ 
    status: 200, 
    description: 'AI testing impact data',
    schema: {
      type: 'object',
      properties: {
        aiTestCases: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              isAIGenerated: { type: 'boolean' },
              count: { type: 'string', example: '45' }
            }
          }
        },
        aiVsManualResults: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              isAIGenerated: { type: 'boolean' },
              status: { type: 'string', example: 'passed' },
              count: { type: 'string', example: '38' }
            }
          }
        }
      }
    }
  })
  async getAITestingImpact(@Request() req) {
    return this.analyticsService.getAITestingImpact(req.user.id);
  }
}
