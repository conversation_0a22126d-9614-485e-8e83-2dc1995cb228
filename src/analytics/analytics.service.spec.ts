// import { Test, TestingModule } from '@nestjs/testing';
// import { getRepositoryToken } from '@nestjs/typeorm';
// import { Repository } from 'typeorm';
// import { NotFoundException } from '@nestjs/common';
// import { AnalyticsService } from './analytics.service';
// import { TestCase } from '../test-cases/test-case.entity';
// import { TestRun } from '../test-runs/test-run.entity';
// import { TestResult } from '../test-results/test-result.entity';
// import { Project } from '../projects/project.entity';

// describe('AnalyticsService', () => {
//   let service: AnalyticsService;
//   let testCaseRepository: Repository<TestCase>;
//   let testRunRepository: Repository<TestRun>;
//   let testResultRepository: Repository<TestResult>;
//   let projectRepository: Repository<Project>;

//   const mockQueryBuilder = {
//     createQueryBuilder: jest.fn().mockReturnThis(),
//     leftJoin: jest.fn().mockReturnThis(),
//     leftJoinAndSelect: jest.fn().mockReturnThis(),
//     select: jest.fn().mockReturnThis(),
//     addSelect: jest.fn().mockReturnThis(),
//     where: jest.fn().mockReturnThis(),
//     andWhere: jest.fn().mockReturnThis(),
//     groupBy: jest.fn().mockReturnThis(),
//     orderBy: jest.fn().mockReturnThis(),
//     having: jest.fn().mockReturnThis(),
//     limit: jest.fn().mockReturnThis(),
//     getRawMany: jest.fn(),
//     getMany: jest.fn(),
//     getOne: jest.fn(),
//   };

//   beforeEach(async () => {
//     const module: TestingModule = await Test.createTestingModule({
//       providers: [
//         AnalyticsService,
//         {
//           provide: getRepositoryToken(TestCase),
//           useValue: {
//             createQueryBuilder: jest.fn(() => mockQueryBuilder),
//           },
//         },
//         {
//           provide: getRepositoryToken(TestRun),
//           useValue: {
//             createQueryBuilder: jest.fn(() => mockQueryBuilder),
//           },
//         },
//         {
//           provide: getRepositoryToken(TestResult),
//           useValue: {
//             createQueryBuilder: jest.fn(() => mockQueryBuilder),
//           },
//         },
//         {
//           provide: getRepositoryToken(Project),
//           useValue: {
//             createQueryBuilder: jest.fn(() => mockQueryBuilder),
//           },
//         },
//       ],
//     }).compile();

//     service = module.get<AnalyticsService>(AnalyticsService);
//     testCaseRepository = module.get<Repository<TestCase>>(getRepositoryToken(TestCase));
//     testRunRepository = module.get<Repository<TestRun>>(getRepositoryToken(TestRun));
//     testResultRepository = module.get<Repository<TestResult>>(getRepositoryToken(TestResult));
//     projectRepository = module.get<Repository<Project>>(getRepositoryToken(Project));
//   });

//   afterEach(() => {
//     jest.clearAllMocks();
//   });

//   describe('getProjectTestCaseAnalytics', () => {
//     const projectId = '123e4567-e89b-12d3-a456-426614174000';
//     const userId = '987fcdeb-51a2-43d1-9f12-345678901234';

//     it('should return test case analytics for a valid project', async () => {
//       // Mock project access verification
//       mockQueryBuilder.getOne.mockResolvedValueOnce({ id: projectId });

//       // Mock analytics data
//       const mockAutomationCoverage = [
//         { type: 'manual', count: '45' },
//         { type: 'automation', count: '32' }
//       ];
//       const mockTypeDistribution = [
//         { type: 'functional', count: '35' },
//         { type: 'integration', count: '25' }
//       ];

//       mockQueryBuilder.getRawMany
//         .mockResolvedValueOnce(mockAutomationCoverage)
//         .mockResolvedValueOnce(mockTypeDistribution)
//         .mockResolvedValueOnce([]) // priority distribution
//         .mockResolvedValueOnce([]) // platform distribution
//         .mockResolvedValueOnce([]); // growth over time

//       const result = await service.getProjectTestCaseAnalytics(projectId, userId);

//       expect(result).toEqual({
//         automationCoverage: mockAutomationCoverage,
//         typeDistribution: mockTypeDistribution,
//         priorityDistribution: [],
//         platformDistribution: [],
//         growthOverTime: []
//       });
//     });

//     it('should throw NotFoundException for invalid project', async () => {
//       mockQueryBuilder.getOne.mockResolvedValueOnce(null);

//       await expect(
//         service.getProjectTestCaseAnalytics(projectId, userId)
//       ).rejects.toThrow(NotFoundException);
//     });
//   });

//   describe('getProjectTestRunPerformance', () => {
//     const projectId = '123e4567-e89b-12d3-a456-426614174000';
//     const userId = '987fcdeb-51a2-43d1-9f12-345678901234';

//     it('should return test run performance data', async () => {
//       // Mock project access verification
//       mockQueryBuilder.getOne.mockResolvedValueOnce({ id: projectId });

//       // Mock test runs
//       const mockTestRuns = [
//         { id: 'run1', name: 'Test Run 1', createdAt: new Date() },
//         { id: 'run2', name: 'Test Run 2', createdAt: new Date() }
//       ];
//       mockQueryBuilder.getMany.mockResolvedValueOnce(mockTestRuns);

//       // Mock performance data
//       const mockPassFail = [
//         { testRunName: 'Test Run 1', status: 'passed', count: '25' },
//         { testRunName: 'Test Run 1', status: 'failed', count: '3' }
//       ];

//       mockQueryBuilder.getRawMany
//         .mockResolvedValueOnce(mockPassFail)
//         .mockResolvedValueOnce([]) // execution time
//         .mockResolvedValueOnce([]); // flaky tests

//       const result = await service.getProjectTestRunPerformance(projectId, userId);

//       expect(result).toEqual({
//         passFail: mockPassFail,
//         executionTime: [],
//         flakyTests: []
//       });
//     });

//     it('should return empty data when no test runs exist', async () => {
//       // Mock project access verification
//       mockQueryBuilder.getOne.mockResolvedValueOnce({ id: projectId });
//       mockQueryBuilder.getMany.mockResolvedValueOnce([]);

//       const result = await service.getProjectTestRunPerformance(projectId, userId);

//       expect(result).toEqual({
//         passFail: [],
//         executionTime: [],
//         flakyTests: []
//       });
//     });
//   });

//   describe('getCrossProjectQualityComparison', () => {
//     const userId = '987fcdeb-51a2-43d1-9f12-345678901234';

//     it('should return quality comparison data for user projects', async () => {
//       // Mock user projects
//       const mockProjects = [
//         { id: 'project1', name: 'Project 1' },
//         { id: 'project2', name: 'Project 2' }
//       ];
//       mockQueryBuilder.getMany.mockResolvedValueOnce(mockProjects);

//       // Mock quality data
//       const mockProjectCoverage = [
//         { projectName: 'Project 1', totalTestCases: '100', automatedTestCases: '75' }
//       ];
//       const mockProjectPassRates = [
//         { projectName: 'Project 1', totalTests: '200', passedTests: '180' }
//       ];

//       mockQueryBuilder.getRawMany
//         .mockResolvedValueOnce(mockProjectCoverage)
//         .mockResolvedValueOnce(mockProjectPassRates);

//       const result = await service.getCrossProjectQualityComparison(userId);

//       expect(result).toEqual({
//         projectCoverage: mockProjectCoverage,
//         projectPassRates: mockProjectPassRates
//       });
//     });

//     it('should return empty data when user has no projects', async () => {
//       mockQueryBuilder.getMany.mockResolvedValueOnce([]);

//       const result = await service.getCrossProjectQualityComparison(userId);

//       expect(result).toEqual({
//         projectCoverage: [],
//         projectPassRates: []
//       });
//     });
//   });

//   describe('getAITestingImpact', () => {
//     const userId = '987fcdeb-51a2-43d1-9f12-345678901234';

//     it('should return AI testing impact data', async () => {
//       // Mock user projects
//       const mockProjects = [{ id: 'project1', name: 'Project 1' }];
//       mockQueryBuilder.getMany.mockResolvedValueOnce(mockProjects);

//       // Mock AI data
//       const mockAITestCases = [
//         { isAIGenerated: true, count: '45' },
//         { isAIGenerated: false, count: '280' }
//       ];
//       const mockAIResults = [
//         { isAIGenerated: true, status: 'passed', count: '38' },
//         { isAIGenerated: true, status: 'failed', count: '7' }
//       ];

//       mockQueryBuilder.getRawMany
//         .mockResolvedValueOnce(mockAITestCases)
//         .mockResolvedValueOnce(mockAIResults);

//       const result = await service.getAITestingImpact(userId);

//       expect(result).toEqual({
//         aiTestCases: mockAITestCases,
//         aiVsManualResults: mockAIResults
//       });
//     });
//   });
// });
