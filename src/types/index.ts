// src/types/index.ts
export interface Tag {
    id: string;
    name: string;
  }
  
  export interface TestCase {
    id: string;
    tcId: number;
    title: string;
    type: string;
    priority: string;
    platform: string;
    testCaseType: string;
    precondition: string;
    steps: string;
    expectation: string;
    folderId: string | null;
    tags: Tag[];
  }
  
  export interface Folder {
    id: string;
    name: string;
    parentId?: string | null;
    children?: Folder[];
  }