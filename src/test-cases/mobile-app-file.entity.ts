import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, <PERSON>inC<PERSON>umn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Project } from '../projects/project.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('mobile_app_files')
export class MobileAppFile {
  @ApiProperty({
    description: 'The unique identifier of the mobile app file',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The project this mobile app file belongs to',
    type: () => Project
  })
  @ManyToOne(() => Project, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @ApiProperty({
    description: 'The ID of the project this mobile app file belongs to',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column()
  projectId: string;

  @ApiProperty({
    description: 'Original filename of the mobile app',
    example: 'MyApp-v1.2.3.apk'
  })
  @Column()
  originalName: string;

  @ApiProperty({
    description: 'File MIME type',
    example: 'application/vnd.android.package-archive'
  })
  @Column()
  mimeType: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 52428800
  })
  @Column({ type: 'bigint' })
  fileSize: number;

  @ApiProperty({
    description: 'Google Cloud Storage URL or local storage path for mobile app files',
    example: 'gs://agentq/test-data/mobile-app-files/project123/MyApp-v1.2.3.apk'
  })
  @Column()
  fileUrl: string;

  @ApiProperty({
    description: 'Mobile platform (android, ios)',
    example: 'android'
  })
  @Column()
  platform: string;

  @ApiProperty({
    description: 'App version if extractable from filename',
    example: 'v1.2.3',
    required: false
  })
  @Column({ nullable: true })
  version: string;

  @ApiProperty({
    description: 'App package name or bundle identifier',
    example: 'com.example.myapp',
    required: false
  })
  @Column({ nullable: true })
  packageName: string;

  @ApiProperty({
    description: 'Description or notes about this mobile app file',
    example: 'Production build for testing',
    required: false
  })
  @Column({ nullable: true })
  description: string;

  @ApiProperty({
    description: 'The date when the mobile app file was uploaded',
    example: '2023-01-01T00:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'The date when the mobile app file record was last updated',
    example: '2023-01-02T00:00:00Z'
  })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({
    description: 'The name of the user who uploaded this mobile app file',
    example: '<EMAIL>'
  })
  @Column({ nullable: true })
  uploadedBy: string;
}
