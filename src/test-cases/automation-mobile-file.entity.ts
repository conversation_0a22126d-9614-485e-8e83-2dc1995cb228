import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>reateDateColumn, UpdateDateColumn } from 'typeorm';
import { TestCase } from './test-case.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('automation_mobile_files')
export class AutomationMobileFile {
  @ApiProperty({
    description: 'The unique identifier of the mobile automation file',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The test case this mobile file belongs to',
    type: () => TestCase
  })
  @ManyToOne(() => TestCase, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'testCaseId' })
  testCase: TestCase;

  @ApiProperty({
    description: 'The ID of the test case this mobile file belongs to',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column()
  testCaseId: string;

  @ApiProperty({
    description: 'The step ID within the mobile automation test case',
    example: '1'
  })
  @Column()
  stepId: string;

  @ApiProperty({
    description: 'Unique mobile file identifier',
    example: 'testcase456-mobile-step-1-1691234567890'
  })
  @Column()
  fileId: string;

  @ApiProperty({
    description: 'Original filename',
    example: 'mobile-screenshot.png'
  })
  @Column()
  originalName: string;

  @ApiProperty({
    description: 'File MIME type',
    example: 'image/png'
  })
  @Column()
  mimeType: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 1024
  })
  @Column()
  fileSize: number;

  @ApiProperty({
    description: 'Google Cloud Storage URL or local storage path for mobile automation files',
    example: 'gs://agentq/test-data/automation-mobile-files/project123/testcase456/step-1/file123-screenshot.png'
  })
  @Column()
  fileUrl: string;

  @ApiProperty({
    description: 'The date when the mobile file was uploaded',
    example: '2023-01-01T00:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'The date when the mobile file record was last updated',
    example: '2023-01-02T00:00:00Z'
  })
  @UpdateDateColumn()
  updatedAt: Date;
}
