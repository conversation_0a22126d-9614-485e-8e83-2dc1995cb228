import { Controller, Get, Post, Body, Put, Param, Delete, UseGuards, Query, ConflictException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { TestCaseTypesService } from './test-case-types.service';
import { TestCaseType } from './test-case-type.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';

@ApiTags('test-case-types')
@ApiBearerAuth()
@Controller('test-case-types')
@UseGuards(JwtAuthGuard)
export class TestCaseTypesController {
  constructor(private readonly testCaseTypesService: TestCaseTypesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new test case type' })
  @ApiResponse({ status: 201, description: 'Test case type created successfully', type: TestCaseType })
  @ApiResponse({ status: 409, description: 'Test case type already exists' })
  async create(@Body() createTestCaseTypeDto: { name: string; description?: string }) {
    try {
      return await this.testCaseTypesService.create(createTestCaseTypeDto);
    } catch (error) {
      if (error instanceof Object && 'code' in error && error.code === '23505') { // Unique violation in PostgreSQL
        throw new ConflictException('Test case type already exists');
      }
      throw error;
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all test case types (paginated)' })
  @ApiResponse({ status: 200, description: 'List of test case types', type: [TestCaseType] })
  findAll(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('search') search?: string
  ) {
    return this.testCaseTypesService.findAll({
      page: +page,
      limit: +limit,
      search
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific test case type' })
  @ApiResponse({ status: 200, description: 'Test case type found', type: TestCaseType })
  findOne(@Param('id') id: string) {
    return this.testCaseTypesService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a test case type' })
  @ApiResponse({ status: 200, description: 'Test case type updated successfully', type: TestCaseType })
  update(
    @Param('id') id: string,
    @Body() updateTestCaseTypeDto: { name?: string; description?: string }
  ) {
    return this.testCaseTypesService.update(id, updateTestCaseTypeDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a test case type' })
  @ApiResponse({ status: 200, description: 'Test case type deleted successfully' })
  remove(@Param('id') id: string) {
    return this.testCaseTypesService.remove(id);
  }
}