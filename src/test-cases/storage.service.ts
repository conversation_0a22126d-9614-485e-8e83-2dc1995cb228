import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Storage } from '@google-cloud/storage';
import { Config } from '../config';

@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);
  private storage: Storage | null = null;
  private bucketName: string;
  private isCloudStorageEnabled: boolean;

  constructor(private readonly configService: ConfigService) {
    this.bucketName = Config.GCP_BUCKET_NAME;
    this.isCloudStorageEnabled = process.env.ENABLE_CLOUD_STORAGE === 'true';

    // Only initialize Google Cloud Storage if enabled and credentials are available
    if (this.isCloudStorageEnabled && this.hasValidGCPCredentials()) {
      try {
        this.storage = new Storage({
          projectId: Config.GCP_PROJECT_ID,
          credentials: {
            client_email: Config.GCP_CLIENT_EMAIL,
            private_key: Config.GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
          },
        });
        this.logger.log('Google Cloud Storage initialized successfully');
      } catch (error) {
        this.logger.warn('Failed to initialize Google Cloud Storage:', error);
        this.storage = null;
        this.isCloudStorageEnabled = false;
      }
    } else {
      this.logger.log('Google Cloud Storage disabled - file will be stored in database only');
    }
  }

  /**
   * Upload file to Google Cloud Storage in the agentq/test-data folder
   * @param fileName - The file path within the bucket
   * @param fileBuffer - The file buffer
   * @param contentType - The file content type
   * @returns Promise<string> - The GCS URL or local storage indicator
   */
  async uploadFile(fileName: string, fileBuffer: Buffer, contentType: string): Promise<string> {
    // If cloud storage is not enabled or not available, return a local storage indicator
    if (!this.isCloudStorageEnabled || !this.storage) {
      this.logger.log(`File stored locally in database: ${fileName}`);
      return `local://agentq/test-data/${fileName}`;
    }

    try {
      // Ensure the file path starts with agentq/test-data
      const fullFileName = fileName.startsWith('test-data/')
        ? fileName
        : `test-data/${fileName}`;

      const file = this.storage.bucket(this.bucketName).file(fullFileName);

      // Upload the file
      await file.save(fileBuffer, {
        metadata: {
          contentType: contentType,
        },
      });

      this.logger.log(`File uploaded successfully to GCS: ${fullFileName}`);
      return `gs://${this.bucketName}/${fullFileName}`;
    } catch (error) {
      this.logger.error(`Failed to upload file to GCS: ${fileName}:`, error);
      // Fallback to local storage indicator
      this.logger.log(`Falling back to local storage for file: ${fileName}`);
      return `local://agentq/test-data/${fileName}`;
    }
  }

  /**
   * Delete file from Google Cloud Storage
   * @param fileName - The file path within the bucket
   * @returns Promise<boolean> - True if deleted successfully, false otherwise
   */
  async deleteFile(fileName: string): Promise<boolean> {
    // If cloud storage is not enabled or not available, return true (assume local deletion handled elsewhere)
    if (!this.isCloudStorageEnabled || !this.storage) {
      this.logger.log(`File deletion skipped (local storage): ${fileName}`);
      return true;
    }

    try {
      // Ensure the file path starts with test-data
      const fullFileName = fileName.startsWith('test-data/')
        ? fileName
        : `test-data/${fileName}`;

      const file = this.storage.bucket(this.bucketName).file(fullFileName);

      // Check if file exists before attempting to delete
      const [exists] = await file.exists();
      if (!exists) {
        this.logger.warn(`File does not exist in GCS: ${fullFileName}`);
        return true; // Consider non-existent file as successfully "deleted"
      }

      // Delete the file
      await file.delete();

      this.logger.log(`File deleted successfully from GCS: ${fullFileName}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete file from GCS: ${fileName}:`, error);
      return false;
    }
  }

  /**
   * List files in a specific folder/prefix
   * @param folderPath - The folder path to list files from
   * @returns Promise<string[]> - Array of file paths
   */
  async listFiles(folderPath: string): Promise<string[]> {
    // If cloud storage is not enabled or not available, return empty array
    if (!this.isCloudStorageEnabled || !this.storage) {
      this.logger.log(`File listing skipped (local storage): ${folderPath}`);
      return [];
    }

    try {
      // Ensure the folder path starts with test-data
      const fullFolderPath = folderPath.startsWith('test-data/')
        ? folderPath
        : `test-data/${folderPath}`;

      // Add trailing slash if not present to ensure we're listing a folder
      const prefix = fullFolderPath.endsWith('/') ? fullFolderPath : `${fullFolderPath}/`;

      const [files] = await this.storage.bucket(this.bucketName).getFiles({
        prefix: prefix,
        delimiter: '/' // Only get files in this exact folder, not subfolders
      });

      const filePaths = files.map(file => file.name);
      this.logger.log(`Found ${filePaths.length} files in folder: ${prefix}`);

      return filePaths;
    } catch (error) {
      this.logger.error(`Failed to list files in folder: ${folderPath}:`, error);
      return [];
    }
  }

  /**
   * Delete all files in a specific folder
   * @param folderPath - The folder path to delete files from
   * @returns Promise<number> - Number of files deleted
   */
  async deleteFilesInFolder(folderPath: string): Promise<number> {
    // If cloud storage is not enabled or not available, return 0
    if (!this.isCloudStorageEnabled || !this.storage) {
      this.logger.log(`Folder deletion skipped (local storage): ${folderPath}`);
      return 0;
    }

    try {
      const filePaths = await this.listFiles(folderPath);

      if (filePaths.length === 0) {
        this.logger.log(`No files found to delete in folder: ${folderPath}`);
        return 0;
      }

      this.logger.log(`Deleting ${filePaths.length} files from folder: ${folderPath}`);

      // Delete all files in parallel
      const deletePromises = filePaths.map(async (filePath) => {
        try {
          const file = this.storage.bucket(this.bucketName).file(filePath);
          await file.delete();
          this.logger.log(`Deleted file: ${filePath}`);
          return 1;
        } catch (error) {
          this.logger.error(`Failed to delete file: ${filePath}:`, error);
          return 0;
        }
      });

      const deleteResults = await Promise.all(deletePromises);
      const deletedCount = deleteResults.reduce((sum: number, result: number) => sum + result, 0);

      this.logger.log(`Folder deletion complete: ${deletedCount}/${filePaths.length} files deleted from ${folderPath}`);

      return deletedCount;
    } catch (error) {
      this.logger.error(`Failed to delete files in folder: ${folderPath}:`, error);
      return 0;
    }
  }

  /**
   * Check if Google Cloud Storage credentials are valid
   */
  private hasValidGCPCredentials(): boolean {
    return !!(Config.GCP_PROJECT_ID && Config.GCP_CLIENT_EMAIL && Config.GCP_PRIVATE_KEY);
  }

}