import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum } from 'class-validator';

export enum MobilePlatform {
  ANDROID = 'android',
  IOS = 'ios'
}

export class UploadMobileAppFileDto {
  @ApiProperty({
    description: 'Mobile platform',
    enum: MobilePlatform,
    example: MobilePlatform.ANDROID
  })
  @IsEnum(MobilePlatform)
  platform: MobilePlatform;

  @ApiProperty({
    description: 'App version if known',
    example: 'v1.2.3',
    required: false
  })
  @IsString()
  @IsOptional()
  version?: string;

  @ApiProperty({
    description: 'App package name or bundle identifier',
    example: 'com.example.myapp',
    required: false
  })
  @IsString()
  @IsOptional()
  packageName?: string;

  @ApiProperty({
    description: 'Description or notes about this mobile app file',
    example: 'Production build for testing',
    required: false
  })
  @IsString()
  @IsOptional()
  description?: string;
}

export class UpdateMobileAppFileDto {
  @ApiProperty({
    description: 'App version if known',
    example: 'v1.2.3',
    required: false
  })
  @IsString()
  @IsOptional()
  version?: string;

  @ApiProperty({
    description: 'App package name or bundle identifier',
    example: 'com.example.myapp',
    required: false
  })
  @IsString()
  @IsOptional()
  packageName?: string;

  @ApiProperty({
    description: 'Description or notes about this mobile app file',
    example: 'Production build for testing',
    required: false
  })
  @IsString()
  @IsOptional()
  description?: string;
}
