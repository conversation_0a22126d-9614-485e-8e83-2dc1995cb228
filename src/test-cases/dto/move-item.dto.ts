import { IsNotEmpty, IsString, <PERSON>Optional, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class MoveItemDto {
  @ApiProperty({
    description: 'Item ID (folder or test case)',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  @IsNotEmpty()
  itemId: string;

  @ApiProperty({
    description: 'Item type (folder or testCase)',
    example: 'folder'
  })
  @IsString()
  @IsNotEmpty()
  itemType: 'folder' | 'testCase';

  @ApiPropertyOptional({
    description: 'Target folder ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsString()
  @IsOptional()
  targetFolderId?: string;
}