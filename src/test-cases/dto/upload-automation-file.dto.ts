import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUUID } from 'class-validator';

export class UploadAutomationFileDto {
  @ApiProperty({
    description: 'The step ID within the automation test case',
    example: '1'
  })
  @IsString()
  @IsNotEmpty()
  stepId: string;

  @ApiProperty({
    description: 'The test case ID this file belongs to',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  @IsNotEmpty()
  testCaseId: string;
}
