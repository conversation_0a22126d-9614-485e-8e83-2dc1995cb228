import { IsNotEmpty, IsString, IsEnum, IsOptional, IsArray, IsUUID, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TestCaseType, Priority, Platform } from '../test-case.entity';

export class CreateTestCaseDto {
  @ApiProperty({
    description: 'Test case title',
    example: 'User Login Validation'
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiPropertyOptional({
    description: 'Test case precondition',
    example: 'User is not logged in'
  })
  @IsString()
  @IsOptional()
  precondition?: string;

  @ApiProperty({
    description: 'Test case steps',
    example: '1. Navigate to login page\n2. Enter credentials\n3. Click login button'
  })
  @IsString()
  @IsNotEmpty()
  steps: string;

  @ApiProperty({
    description: 'Expected results',
    example: 'User should be redirected to dashboard'
  })
  @IsString()
  @IsNotEmpty()
  expectation: string;

  @ApiProperty({
    description: 'Test case priority',
    enum: Priority,
    example: Priority.HIGH
  })
  @IsEnum(Priority)
  priority: Priority;

  @ApiProperty({
    description: 'Test case type',
    example: 'functional'
  })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty({
    description: 'Platform',
    enum: Platform,
    example: Platform.WEB
  })
  @IsEnum(Platform)
  platform: Platform;

  @ApiProperty({
    description: 'Test case automation status',
    enum: TestCaseType,
    example: TestCaseType.MANUAL
  })
  @IsEnum(TestCaseType)
  testCaseType: TestCaseType;

  @ApiPropertyOptional({
    description: 'Folder ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsString()
  @IsOptional()
  folderId?: string;

  @ApiProperty({
  description: 'Whether the test case is automated by AgentQ',
  example: false
  })
  @IsBoolean()
  automationByAgentq: boolean;

  @ApiPropertyOptional({
    description: 'Tag IDs',
    example: ['123e4567-e89b-12d3-a456-************'],
    type: [String]
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsOptional()
  tags?: string[];
}
