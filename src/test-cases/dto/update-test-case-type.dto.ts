import { IsString, IsOptional } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateTestCaseTypeDto {
  @ApiPropertyOptional({
    description: 'Test case type name',
    example: 'functional'
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Test case type description',
    example: 'Tests that verify the functional requirements'
  })
  @IsString()
  @IsOptional()
  description?: string;
}