import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString, IsUUID, ValidateNested, IsNumber, IsOptional, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

class MobileAutomationStep {
  @ApiProperty({
    description: 'Step number',
    example: 1
  })
  @IsNumber()
  step: number;

  @ApiProperty({
    description: 'Step name/description',
    example: 'Launch mobile app'
  })
  @IsString()
  stepName: string;

  @ApiProperty({
    description: 'Target selector for the mobile action (xpath, id, accessibility id, etc.)',
    example: '//android.widget.Button[@text="Login"]',
    required: false
  })
  @IsString()
  @IsOptional()
  target: string;

  @ApiProperty({
    description: 'Value for the mobile action (app package, text to input, etc.)',
    example: 'com.example.app',
    required: false
  })
  @IsString()
  @IsOptional()
  value: string;

  @ApiProperty({
    description: 'Prompt for AI-assisted mobile steps',
    example: 'Check if the login button is visible on the mobile screen',
    required: false
  })
  @IsString()
  @IsOptional()
  prompt: string;

  @ApiProperty({
    description: 'Flag to show interaction dropdown for mobile actions',
    example: false,
    required: false
  })
  @IsBoolean()
  @IsOptional()
  showInteractionDropdown: boolean;

  @ApiProperty({
    description: 'Flag to show assertion dropdown for mobile testing',
    example: false,
    required: false
  })
  @IsBoolean()
  @IsOptional()
  showAssertionDropdown: boolean;

  @ApiProperty({
    description: 'Flag to show prompt field for mobile automation',
    example: false,
    required: false
  })
  @IsBoolean()
  @IsOptional()
  showPromptField: boolean;

  @ApiProperty({
    description: 'Mobile action type (launchApp, tap, swipe, input, assertText, etc.)',
    example: 'launchApp',
    required: false
  })
  @IsString()
  @IsOptional()
  action?: string;

  @ApiProperty({
    description: 'Multiple mobile actions for this step in JSON format',
    example: '[{"action":"launchApp","target":"","value":"com.example.app"},{"action":"tap","target":"//android.widget.Button[@text=\\"Login\\"]","value":""}]',
    required: false
  })
  @IsString()
  @IsOptional()
  Actions?: string;

  @ApiProperty({
    description: 'URL of uploaded file for this mobile automation step',
    example: 'gs://agentq/test-data/mobile-automation-files/project123/testcase456/step-2/file123-screenshot.png',
    required: false
  })
  @IsString()
  @IsOptional()
  fileUrl?: string;

  @ApiProperty({
    description: 'Unique identifier of uploaded file for this mobile automation step',
    example: 'testcase456-mobile-step-2-1691234567890',
    required: false
  })
  @IsString()
  @IsOptional()
  fileId?: string;

  @ApiProperty({
    description: 'Mobile platform (android, ios)',
    example: 'android',
    required: false
  })
  @IsString()
  @IsOptional()
  platform?: string;

  @ApiProperty({
    description: 'Device orientation (portrait, landscape)',
    example: 'portrait',
    required: false
  })
  @IsString()
  @IsOptional()
  orientation?: string;

  @ApiProperty({
    description: 'Coordinates for tap/swipe actions',
    example: '{"x": 100, "y": 200}',
    required: false
  })
  @IsString()
  @IsOptional()
  coordinates?: string;
}

export class AutomationMobileTestCaseDto {
  @ApiProperty({
    description: 'The ID of the test case this mobile automation belongs to',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  @IsNotEmpty()
  testCaseId: string;

  @ApiProperty({
    description: 'The mobile automation steps',
    type: [MobileAutomationStep],
    example: [
      {
        step: 1,
        stepName: 'Launch mobile app',
        action: 'launchApp',
        value: 'com.example.app',
        target: '',
        prompt: '',
        showInteractionDropdown: false,
        showAssertionDropdown: false,
        showPromptField: false,
        platform: 'android',
        orientation: 'portrait'
      }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MobileAutomationStep)
  steps: MobileAutomationStep[];
}
