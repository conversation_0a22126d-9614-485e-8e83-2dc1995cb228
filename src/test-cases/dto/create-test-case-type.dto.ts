import { IsNotEmpty, IsString, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateTestCaseTypeDto {
  @ApiProperty({
    description: 'Test case type name',
    example: 'functional'
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'Test case type description',
    example: 'Tests that verify the functional requirements'
  })
  @IsString()
  @IsOptional()
  description?: string;
}