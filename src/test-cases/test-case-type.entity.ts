import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('test_case_types')
export class TestCaseType {
  @ApiProperty({
    description: 'The unique identifier of the test case type',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Test case type name',
    example: 'functional'
  })
  @Column({ unique: true })
  name: string;

  @ApiProperty({
    description: 'Test case type description',
    example: 'Tests that verify the functional requirements'
  })
  @Column({ type: 'text', nullable: true })
  description: string;

  @ApiProperty({
    description: 'When the test case type was created',
    example: '2024-02-20T12:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When the test case type was last updated',
    example: '2024-02-20T12:00:00Z'
  })
  @UpdateDateColumn()
  updatedAt: Date;
}