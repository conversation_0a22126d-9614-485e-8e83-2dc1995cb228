import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { TestCaseType } from './test-case-type.entity';

@Injectable()
export class TestCaseTypesService {
  constructor(
    @InjectRepository(TestCaseType)
    private testCaseTypesRepository: Repository<TestCaseType>,
  ) {}

  async create(createTestCaseTypeDto: { name: string; description?: string }): Promise<TestCaseType> {
    const testCaseType = this.testCaseTypesRepository.create(createTestCaseTypeDto);
    return this.testCaseTypesRepository.save(testCaseType);
  }

  async findAll(options: { page: number; limit: number; search?: string }) {
    const { page, limit, search } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.testCaseTypesRepository.createQueryBuilder('test_case_type');

    if (search) {
      queryBuilder.where(
        'test_case_type.name ILIKE :search OR test_case_type.description ILIKE :search',
        { search: `%${search}%` }
      );
    }

    const [types, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .orderBy('test_case_type.name', 'ASC')
      .getManyAndCount();

    return {
      data: types,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };
  }

  async findOne(id: string): Promise<TestCaseType> {
    const testCaseType = await this.testCaseTypesRepository.findOne({
      where: { id }
    });

    if (!testCaseType) {
      throw new NotFoundException('Test case type not found');
    }

    return testCaseType;
  }

  async update(id: string, updateTestCaseTypeDto: { name?: string; description?: string }): Promise<TestCaseType> {
    const testCaseType = await this.findOne(id);
    Object.assign(testCaseType, updateTestCaseTypeDto);
    return this.testCaseTypesRepository.save(testCaseType);
  }

  async remove(id: string): Promise<void> {
    const testCaseType = await this.findOne(id);
    await this.testCaseTypesRepository.remove(testCaseType);
  }
}