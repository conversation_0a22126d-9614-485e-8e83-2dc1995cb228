import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, ConflictException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { TagsService } from './tag.service';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { Tag } from './tag.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';

@ApiTags('tags')
@ApiBearerAuth()
@Controller('tags')
@UseGuards(JwtAuthGuard)
export class TagsController {
  constructor(private readonly tagsService: TagsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new tag' })
  @ApiResponse({ status: 201, description: 'Tag created successfully', type: Tag })
  @ApiResponse({ status: 409, description: 'Tag already exists' })
  async create(@Body() createTagDto: CreateTagDto) {
    try {
      return await this.tagsService.create(createTagDto);
    } catch (error) {
      if (error instanceof Object && 'code' in error && error.code === '23505') { // Unique violation in PostgreSQL
        throw new ConflictException('Tag already exists');
      }
      throw error;
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all tags' })
  @ApiResponse({ status: 200, description: 'List of tags', type: [Tag] })
  findAll() {
    return this.tagsService.findAll();
  }

  @Get('projects/:projectId')
  @ApiOperation({ summary: 'Get all tags used in a specific project' })
  @ApiResponse({ status: 200, description: 'List of project tags', type: [Tag] })
  @ApiResponse({ status: 404, description: 'Project not found' })
  getProjectTags(@Param('projectId') projectId: string, @Request() req) {
    return this.tagsService.findProjectTags(projectId, req.user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific tag' })
  @ApiResponse({ status: 200, description: 'Tag found', type: Tag })
  findOne(@Param('id') id: string) {
    return this.tagsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a tag' })
  @ApiResponse({ status: 200, description: 'Tag updated successfully', type: Tag })
  update(@Param('id') id: string, @Body() updateTagDto: UpdateTagDto) {
    return this.tagsService.update(id, updateTagDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a tag' })
  @ApiResponse({ status: 200, description: 'Tag deleted successfully' })
  remove(@Param('id') id: string) {
    return this.tagsService.remove(id);
  }

  @Post('projects/:projectId/test-cases/:testCaseId/tags')
  @ApiOperation({ summary: 'Add tags to a test case' })
  @ApiResponse({ status: 200, description: 'Tags added successfully' })
  async addTagsToTestCase(
    @Param('projectId') projectId: string,
    @Param('testCaseId') testCaseId: string,
    @Body() body: { tagIds: string[] },
    @Request() req,
  ) {
    return this.tagsService.addTagsToTestCase(projectId, testCaseId, body.tagIds, req.user.id);
  }

  @Delete('projects/:projectId/test-cases/:testCaseId/tags/:tagId')
  @ApiOperation({ summary: 'Remove a tag from a test case' })
  @ApiResponse({ status: 200, description: 'Tag removed successfully' })
  async removeTagFromTestCase(
    @Param('projectId') projectId: string,
    @Param('testCaseId') testCaseId: string,
    @Param('tagId') tagId: string,
    @Request() req,
  ) {
    return this.tagsService.removeTagFromTestCase(projectId, testCaseId, tagId, req.user.id);
  }
}