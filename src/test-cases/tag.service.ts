import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Tag } from './tag.entity';
import { TestCase } from './test-case.entity';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { ProjectsService } from '../projects/projects.service';
import { UsersService } from '../users/user.service';

@Injectable()
export class TagsService {
  constructor(
    @InjectRepository(Tag)
    private tagsRepository: Repository<Tag>,
    @InjectRepository(TestCase)
    private testCasesRepository: Repository<TestCase>,
    private projectsService: ProjectsService,
    private usersService: UsersService,
  ) {}

  async create(createTagDto: CreateTagDto): Promise<Tag> {
    const tag = this.tagsRepository.create(createTagDto);
    return this.tagsRepository.save(tag);
  }

  async findAll(): Promise<Tag[]> {
    return this.tagsRepository.find({
      order: {
        name: 'ASC',
      },
    });
  }

  async findProjectTags(projectId: string, userId: string): Promise<Tag[]> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);
    
    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    // Get all tags used in the project's test cases
    const tags = await this.tagsRepository
      .createQueryBuilder('tag')
      .innerJoin('tag.testCases', 'testCase')
      .where('testCase.projectId = :projectId', { projectId })
      .orderBy('tag.name', 'ASC')
      .getMany();

    return tags;
  }

  async findOne(id: string): Promise<Tag> {
    const tag = await this.tagsRepository.findOne({
      where: { id },
      relations: ['testCases'],
    });

    if (!tag) {
      throw new NotFoundException('Tag not found');
    }

    return tag;
  }

  async update(id: string, updateTagDto: UpdateTagDto): Promise<Tag> {
    const tag = await this.findOne(id);
    Object.assign(tag, updateTagDto);
    return this.tagsRepository.save(tag);
  }

  async remove(id: string): Promise<void> {
    const tag = await this.findOne(id);
    await this.tagsRepository.remove(tag);
  }

  async addTagsToTestCase(projectId: string, testCaseId: string, tagIds: string[], userId: string): Promise<TestCase> {
    // Verify project exists and belongs to user
    await this.projectsService.findOne(projectId, userId);

    // Find the test case
    const testCase = await this.testCasesRepository.findOne({
      where: { id: testCaseId, projectId },
      relations: ['tags'],
    });

    if (!testCase) {
      throw new NotFoundException('Test case not found');
    }

    // Find all tags
    const tags = await this.tagsRepository.findByIds(tagIds);
    if (tags.length !== tagIds.length) {
      throw new NotFoundException('One or more tags not found');
    }

    // Add new tags to existing ones
    testCase.tags = [...(testCase.tags || []), ...tags];
    return this.testCasesRepository.save(testCase);
  }

  async removeTagFromTestCase(projectId: string, testCaseId: string, tagId: string, userId: string): Promise<TestCase> {
    // Verify project exists and belongs to user
    await this.projectsService.findOne(projectId, userId);

    // Find the test case
    const testCase = await this.testCasesRepository.findOne({
      where: { id: testCaseId, projectId },
      relations: ['tags'],
    });

    if (!testCase) {
      throw new NotFoundException('Test case not found');
    }

    // Remove the tag
    testCase.tags = testCase.tags.filter(tag => tag.id !== tagId);
    return this.testCasesRepository.save(testCase);
  }

  // Helper method to get user with companyId
  private async getUserWithCompanyId(userId: string): Promise<{ id: string; companyId: string }> {
    const user = await this.usersService.findById(userId);
    
    if (!user || !user.companyId) {
      throw new NotFoundException(`User with ID ${userId} not found or has no company`);
    }
    
    return { id: user.id, companyId: user.companyId };
  }
}
