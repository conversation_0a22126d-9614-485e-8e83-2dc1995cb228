import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { TestCase } from './test-case.entity';

@Entity('tags')
export class Tag {
  @ApiProperty({
    description: 'The unique identifier of the tag',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Tag name',
    example: 'regression'
  })
  @Column({ unique: true })
  name: string;

  @ManyToMany(() => TestCase, testCase => testCase.tags)
  testCases: TestCase[];

  @ApiProperty({
    description: 'When the tag was created',
    example: '2024-02-20T12:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When the tag was last updated',
    example: '2024-02-20T12:00:00Z'
  })
  @UpdateDateColumn()
  updatedAt: Date;
}