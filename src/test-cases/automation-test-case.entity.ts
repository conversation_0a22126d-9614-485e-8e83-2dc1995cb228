import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON>n, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { TestCase } from './test-case.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('automation_test_cases')
export class AutomationTestCase {
  @ApiProperty({
    description: 'The unique identifier of the automation test case',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The test case this automation belongs to',
    type: () => TestCase
  })
  @ManyToOne(() => TestCase, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'testCaseId' })
  testCase: TestCase;

  @ApiProperty({
    description: 'The ID of the test case this automation belongs to',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column()
  testCaseId: string;

  @ApiProperty({
    description: 'The automation steps in JSON format',
    example: '[{"step":1,"stepName":"Navigate to login page","action":"goto","value":"https://example.com/login"}]'
  })
  @Column({ type: 'json' })
  steps: any;

  @ApiProperty({
    description: 'The date when the automation test case was created',
    example: '2023-01-01T00:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'The date when the automation test case was last updated',
    example: '2023-01-02T00:00:00Z'
  })
  @UpdateDateColumn()
  updatedAt: Date;
}