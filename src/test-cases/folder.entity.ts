import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Project } from '../projects/project.entity';
import { TestCase } from './test-case.entity';

@Entity('test_case_folders')
export class TestCaseFolder {
  @ApiProperty({
    description: 'The unique identifier of the folder',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Folder name',
    example: 'Login Tests'
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'Parent folder ID',
    example: '123e4567-e89b-12d3-a456-************',
    nullable: true
  })
  @Column({ nullable: true })
  parentId: string;

  @ManyToOne(() => TestCaseFolder, folder => folder.children, { onDelete: 'CASCADE' })
  parent: TestCaseFolder;

  @OneToMany(() => TestCaseFolder, folder => folder.parent)
  children: TestCaseFolder[];

  @OneToMany(() => TestCase, testCase => testCase.folder)
  testCases: TestCase[];

  @ManyToOne(() => Project, { onDelete: 'CASCADE' })
  project: Project;

  @Column()
  projectId: string;

  @ApiProperty({
    description: 'When the folder was created',
    example: '2024-02-20T12:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When the folder was last updated',
    example: '2024-02-20T12:00:00Z'
  })
  @UpdateDateColumn()
  updatedAt: Date;
}