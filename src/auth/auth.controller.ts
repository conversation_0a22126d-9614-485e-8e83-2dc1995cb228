import { Controller, Post, Body, UseGuards, Request, Get, Delete, Param, Headers, UnauthorizedException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guards';
import { CreateUserDto } from '../users/dto/create-user.dto';
import { LoginDto } from './dto/login.dto';
import { AuthResponseDto } from './dto/auth-response.dto';
import { User } from '../users/user.entity';
import { ConfigService } from '@nestjs/config';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService, private configService: ConfigService) {}

  @UseGuards(LocalAuthGuard)
  @Post('login')
  @ApiOperation({ summary: 'User login' })
  @ApiResponse({ status: 200, description: 'Login successful', type: AuthResponseDto })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async login(@Body() loginDto: LoginDto, @Request() req: any) {
    return this.authService.login(req.user);
  }

  @Post('register')
  @ApiOperation({ summary: 'User registration' })
  @ApiResponse({ status: 201, description: 'User registered successfully', type: AuthResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiResponse({ status: 409, description: 'Email already exists' })
  async register(@Body() createUserDto: CreateUserDto) {
    // Check if companyId exists in the request
    if (createUserDto.companyId) {
      // Validate if company exists
      const company = await this.authService.validateCompany(createUserDto.companyId);
      if (!company) {
        // Company doesn't exist, either create one or set companyId to null
        createUserDto.companyId = null;
      }
    }
    return this.authService.register(createUserDto);
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'Profile retrieved successfully', type: User })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getProfile(@Request() req: any) {
    return await this.authService.getProfile(req.user.id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('verify')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Verify JWT token' })
  @ApiResponse({ status: 200, description: 'Token is valid' })
  @ApiResponse({ status: 401, description: 'Invalid token' })
  async verifyToken(@Request() req: any) {
    // If we reach here, the JWT token is valid (JwtAuthGuard passed)
    return {
      valid: true,
      user: {
        id: req.user.id,
        email: req.user.email,
        role: req.user.role,
        name: req.user.name
      }
    };
  }

  @Delete('users/:email')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a user by email' })
  @ApiResponse({ status: 200, description: 'User deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async deleteUser(
    @Param('email') email: string, 
    @Request() req: any,
    @Headers('x-api-key') apiKey?: string
  ) {
    // Check if using JWT auth or API key
    if (apiKey) {
      // Validate API key against JWT_SECRET
      const isValidApiKey = apiKey === this.configService.get('JWT_SECRET');
      if (!isValidApiKey) {
        throw new UnauthorizedException('Invalid API key');
      }
      // If valid API key, proceed with deletion as admin
      return this.authService.deleteUserByEmail(email, { role: 'superadmin' });
    }
    
    // Otherwise use JWT auth guard
    if (!req.user) {
      throw new UnauthorizedException('Authentication required');
    }
    
    return this.authService.deleteUserByEmail(email, req.user);
  }

  @Post('token/refresh')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({ status: 200, description: 'Token refreshed successfully', type: AuthResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async refreshToken(@Request() req: any) {
    return this.authService.login(req.user);
  }
}
