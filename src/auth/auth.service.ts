import { Injectable, UnauthorizedException, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { UsersService } from '../users/user.service';
import { CreateUserDto } from '../users/dto/create-user.dto';
import { User } from '../users/user.entity';
import { CompaniesService } from '../companies/companies.service';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private companiesService: CompaniesService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.usersService.findOne(email);
    
    if (user && await bcrypt.compare(password, user.password)) {
      const { password, ...result } = user;
      return result;
    }
    
    return null;
  }

  async validateCompany(companyId: string) {
  return this.companiesService.findById(companyId);
}

  async login(user: any) {
    console.log('Login for user:', user.email, 'with ID:', user.id);
    const payload = { 
      email: user.email, 
      sub: user.id, 
      role: user.role || 'user',
      name: user.name,
      external_auth: true, // Add this flag to indicate it's from our auth service
      companyId: user.companyId
    };
    
    return {
      user,
      access_token: this.jwtService.sign(payload),
    };
  }

  async register(createUserDto: CreateUserDto) {
    const user = await this.usersService.create(createUserDto);
    
    // Remove password from response
    const { password, ...result } = user;
    
    // Generate JWT token
    const payload = { 
      email: user.email, 
      sub: user.id, 
      role: user.role || 'user',
      name: user.name,
      external_auth: true, // Add this flag to indicate it's from our auth service
      companyId: user.companyId
    };
    console.log('Register payload:', payload);
    
    return {
      user: result,
      access_token: this.jwtService.sign(payload),
    };
  }

  async deleteUserByEmail(email: string, currentUser: any): Promise<{ message: string }> {
    // Check if user is deleting themselves or has admin privileges
    if (email !== currentUser.email && currentUser.role !== 'superadmin') {
      throw new UnauthorizedException('only superadmin able delete user');
    }
    
    const user = await this.usersService.findOne(email);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    await this.usersService.removeByEmail(email);
    return { message: 'User deleted successfully' };
  }

  async getProfile(userId: string): Promise<User> {
    return this.usersService.getProfile(userId);
  }
}
