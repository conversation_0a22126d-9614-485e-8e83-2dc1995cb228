import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TestRunsController } from './test-runs.controller';
import { TestRunsService } from './test-runs.service';
import { TestRun } from './test-run.entity';
import { TestResult } from '../test-results/test-result.entity';
import { TestResultHistory } from '../test-results/test-result-history.entity';
import { TestCase } from '../test-cases/test-case.entity';
import { ProjectsModule } from '../projects/projects.module';
import { IntegrationsModule } from '../integrations/integrations.module';
import { Issue } from '../test-results/issue.entity';
import { IssueUpdaterService } from '../test-results/issue-updater.service';
import { UsersModule } from '../users/user.module';
import { TestResultsModule } from '../test-results/test-results.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([TestRun, TestResult, TestResultHistory, TestCase, Issue]),
    ProjectsModule,
    IntegrationsModule,
    UsersModule,
    forwardRef(() => TestResultsModule),
  ],
  controllers: [TestRunsController],
  providers: [TestRunsService, IssueUpdaterService],
  exports: [TestRunsService],
})
export class TestRunModule {}
