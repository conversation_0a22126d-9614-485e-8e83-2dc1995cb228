import { ApiProperty } from '@nestjs/swagger';

export class CreateIssueDto {
  @ApiProperty({
    description: 'The test result ID this issue is linked to',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  testResultId: string;

  @ApiProperty({
    description: 'The Jira issue ID',
    example: '10000',
    required: false
  })
  jiraIssueId?: string;

  @ApiProperty({
    description: 'The Jira issue key',
    example: 'PROJ-123',
    required: false
  })
  jiraIssueKey?: string;

  @ApiProperty({
    description: 'The URL to the Jira issue',
    example: 'https://your-domain.atlassian.net/browse/PROJ-123',
    required: false
  })
  jiraIssueUrl?: string;

  @ApiProperty({
    description: 'The summary/title of the issue',
    example: 'Login button not working'
  })
  summary: string;

  @ApiProperty({
    description: 'The detailed description of the issue',
    example: 'When clicking the login button, nothing happens. Expected to be redirected to dashboard.'
  })
  description: string;

  @ApiProperty({
    description: 'The current status of the issue',
    example: 'To Do',
    required: false
  })
  status?: string;
}

export class UpdateIssueDto {
  @ApiProperty({
    description: 'The Jira issue ID',
    example: '10000',
    required: false
  })
  jiraIssueId?: string;

  @ApiProperty({
    description: 'The Jira issue key',
    example: 'PROJ-123',
    required: false
  })
  jiraIssueKey?: string;

  @ApiProperty({
    description: 'The URL to the Jira issue',
    example: 'https://your-domain.atlassian.net/browse/PROJ-123',
    required: false
  })
  jiraIssueUrl?: string;

  @ApiProperty({
    description: 'The summary/title of the issue',
    example: 'Login button not working',
    required: false
  })
  summary?: string;

  @ApiProperty({
    description: 'The detailed description of the issue',
    example: 'When clicking the login button, nothing happens. Expected to be redirected to dashboard.',
    required: false
  })
  description?: string;

  @ApiProperty({
    description: 'The current status of the issue',
    example: 'To Do',
    required: false
  })
  status?: string;
}

export class IssueResponseDto {
  @ApiProperty({
    description: 'The unique identifier of the issue',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({
    description: 'The test result ID this issue is linked to',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  testResultId: string;

  @ApiProperty({
    description: 'The test run ID this issue is linked to',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  testRunId: string;

  @ApiProperty({
    description: 'The sequential defect ID within the test run',
    example: 1
  })
  defectId: number;

  @ApiProperty({
    description: 'The Jira issue ID',
    example: '10000'
  })
  jiraIssueId: string;

  @ApiProperty({
    description: 'The Jira issue key',
    example: 'PROJ-123'
  })
  jiraIssueKey: string;

  @ApiProperty({
    description: 'The URL to the Jira issue',
    example: 'https://your-domain.atlassian.net/browse/PROJ-123'
  })
  jiraIssueUrl: string;

  @ApiProperty({
    description: 'The summary/title of the issue',
    example: 'Login button not working'
  })
  summary: string;

  @ApiProperty({
    description: 'The detailed description of the issue',
    example: 'When clicking the login button, nothing happens. Expected to be redirected to dashboard.'
  })
  description: string;

  @ApiProperty({
    description: 'The current status of the issue',
    example: 'To Do'
  })
  status: string;

  @ApiProperty({
    description: 'When the issue was created',
    example: '2024-03-17T06:22:49.000Z'
  })
  createdAt: Date;

  @ApiProperty({
    description: 'When the issue was last updated',
    example: '2024-03-17T06:22:51.000Z'
  })
  updatedAt: Date;
}