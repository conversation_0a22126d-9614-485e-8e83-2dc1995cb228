import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class KubernetesCleanupService {
  private readonly logger = new Logger(KubernetesCleanupService.name);
  private readonly dastServiceUrl: string;

  constructor() {
    // URL of the DAST service (websocket_ai_dast_test_run)
    this.dastServiceUrl = process.env.DAST_SERVICE_URL || 'http://localhost:3024';
  }

  /**
   * Clean up Kubernetes resources after DAST test completion
   * This calls the DAST service to clean up pods and services
   */
  async cleanupDastResources(clientId: string): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log(`🧹 Initiating cleanup for CLIENT_ID: ${clientId}`);

      // Call the DAST service cleanup endpoint
      const response = await axios.post(
        `${this.dastServiceUrl}/api/cleanup/kubernetes`,
        { clientId },
        {
          timeout: 30000, // 30 second timeout
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.status === 200 && response.data.success) {
        this.logger.log(`✅ Successfully cleaned up Kubernetes resources for CLIENT_ID: ${clientId}`);
        return {
          success: true,
          message: `Kubernetes resources cleaned up successfully for CLIENT_ID: ${clientId}`,
        };
      } else {
        this.logger.warn(`⚠️ Cleanup response indicates failure for CLIENT_ID: ${clientId}`, response.data);
        return {
          success: false,
          message: `Cleanup failed: ${response.data.message || 'Unknown error'}`,
        };
      }
    } catch (error) {
      this.logger.error(`❌ Failed to cleanup Kubernetes resources for CLIENT_ID: ${clientId}`, error);
      
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          return {
            success: false,
            message: 'DAST service is not available. Kubernetes resources may need manual cleanup.',
          };
        } else if (error.response) {
          return {
            success: false,
            message: `DAST service error: ${error.response.status} - ${error.response.data?.message || error.message}`,
          };
        }
      }

      return {
        success: false,
        message: `Cleanup failed: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Clean up orphaned Kubernetes resources older than specified minutes
   * This is useful for cleaning up resources that may have been left behind
   */
  async cleanupOrphanedResources(olderThanMinutes: number = 10): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log(`🧹 Initiating cleanup of orphaned resources older than ${olderThanMinutes} minutes`);

      // Call the DAST service orphaned cleanup endpoint
      const response = await axios.post(
        `${this.dastServiceUrl}/api/cleanup/orphaned`,
        { olderThanMinutes },
        {
          timeout: 60000, // 60 second timeout for orphaned cleanup
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.status === 200 && response.data.success) {
        this.logger.log(`✅ Successfully cleaned up orphaned Kubernetes resources`);
        return {
          success: true,
          message: `Orphaned Kubernetes resources cleaned up successfully`,
        };
      } else {
        this.logger.warn(`⚠️ Orphaned cleanup response indicates failure`, response.data);
        return {
          success: false,
          message: `Orphaned cleanup failed: ${response.data.message || 'Unknown error'}`,
        };
      }
    } catch (error) {
      this.logger.error(`❌ Failed to cleanup orphaned Kubernetes resources`, error);
      
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          return {
            success: false,
            message: 'DAST service is not available. Orphaned resources may need manual cleanup.',
          };
        } else if (error.response) {
          return {
            success: false,
            message: `DAST service error: ${error.response.status} - ${error.response.data?.message || error.message}`,
          };
        }
      }

      return {
        success: false,
        message: `Orphaned cleanup failed: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Check the status of DAST service connectivity
   */
  async checkDastServiceHealth(): Promise<{ available: boolean; message: string }> {
    try {
      const response = await axios.get(`${this.dastServiceUrl}/health`, {
        timeout: 5000, // 5 second timeout
      });

      if (response.status === 200) {
        this.logger.log(`✅ DAST service is healthy`);
        return {
          available: true,
          message: 'DAST service is available and healthy',
        };
      } else {
        this.logger.warn(`⚠️ DAST service returned unexpected status: ${response.status}`);
        return {
          available: false,
          message: `DAST service returned status: ${response.status}`,
        };
      }
    } catch (error) {
      this.logger.error(`❌ DAST service health check failed`, error);
      
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          return {
            available: false,
            message: 'DAST service is not available (connection refused)',
          };
        } else if (error.response) {
          return {
            available: false,
            message: `DAST service error: ${error.response.status}`,
          };
        }
      }

      return {
        available: false,
        message: `Health check failed: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Get the list of active ZAP jobs from the DAST service
   */
  async getActiveZapJobs(): Promise<{ success: boolean; jobs: string[]; message: string }> {
    try {
      const response = await axios.get(`${this.dastServiceUrl}/api/zap/jobs`, {
        timeout: 10000, // 10 second timeout
      });

      if (response.status === 200 && response.data.success) {
        return {
          success: true,
          jobs: response.data.jobs || [],
          message: 'Successfully retrieved active ZAP jobs',
        };
      } else {
        return {
          success: false,
          jobs: [],
          message: `Failed to retrieve jobs: ${response.data.message || 'Unknown error'}`,
        };
      }
    } catch (error) {
      this.logger.error(`❌ Failed to get active ZAP jobs`, error);
      
      return {
        success: false,
        jobs: [],
        message: `Failed to get jobs: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }
}
