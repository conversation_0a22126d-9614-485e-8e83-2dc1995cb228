import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { TestResult } from './test-result.entity';
import { TestRun } from '../test-runs/test-run.entity';

@Entity('issues')
export class Issue {
  @ApiProperty({
    description: 'The unique identifier of the issue',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => TestResult, { onDelete: 'CASCADE' })
  testResult: TestResult;

  @Column()
  testResultId: string;

  @ApiProperty({
    description: 'The sequential defect ID within the test run',
    example: 1
  })
  @Column({ nullable: true })
  defectId: number;

  @ManyToOne(() => TestRun, { onDelete: 'CASCADE', nullable: true })
  testRun: TestRun;

  @Column({ nullable: true })
  testRunId: string;

  @ApiProperty({
    description: 'The Jira issue ID',
    example: '10000'
  })
  @Column({ nullable: true })
  jiraIssueId: string;

  @ApiProperty({
    description: 'The Jira issue key',
    example: 'PROJ-123'
  })
  @Column({ nullable: true })
  jiraIssueKey: string;

  @ApiProperty({
    description: 'The URL to the Jira issue',
    example: 'https://your-domain.atlassian.net/browse/PROJ-123'
  })
  @Column({ nullable: true })
  jiraIssueUrl: string;

  @ApiProperty({
    description: 'The summary/title of the issue',
    example: 'Login button not working'
  })
  @Column()
  summary: string;

  @ApiProperty({
    description: 'The detailed description of the issue',
    example: 'When clicking the login button, nothing happens. Expected to be redirected to dashboard.'
  })
  @Column('text')
  description: string;

  @ApiProperty({
    description: 'The current status of the issue',
    example: 'To Do'
  })
  @Column({ nullable: true })
  status: string;

  @ApiProperty({
    description: 'When the issue was created',
    example: '2024-03-17T06:22:49.000Z'
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When the issue was last updated',
    example: '2024-03-17T06:22:51.000Z'
  })
  @UpdateDateColumn()
  updatedAt: Date;
}