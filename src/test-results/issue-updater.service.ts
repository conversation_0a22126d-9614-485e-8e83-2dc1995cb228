import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Issue } from './issue.entity';
import { TestResult } from './test-result.entity';
import { TestRun } from '../test-runs/test-run.entity';

@Injectable()
export class IssueUpdaterService implements OnModuleInit {
  private readonly logger = new Logger(IssueUpdaterService.name);

  constructor(
    @InjectRepository(Issue)
    private issueRepository: Repository<Issue>,
    @InjectRepository(TestResult)
    private testResultRepository: Repository<TestResult>,
    @InjectRepository(TestRun)
    private testRunRepository: Repository<TestRun>,
  ) {}

  async onModuleInit() {
    // Wait a bit to ensure database connection is established
    setTimeout(() => this.updateExistingIssues(), 5000);
  }

  async updateExistingIssues() {
    try {
      this.logger.log('Starting to update existing issues with testRunId and defectId...');

      // Find all issues that don't have a testRunId
      const issues = await this.issueRepository.find({
        where: { testRunId: null }
      });

      if (issues.length === 0) {
        this.logger.log('No issues need updating.');
        return;
      }

      this.logger.log(`Found ${issues.length} issues that need updating.`);

      // Process issues in batches to avoid memory issues
      const batchSize = 100;
      for (let i = 0; i < issues.length; i += batchSize) {
        const batch = issues.slice(i, i + batchSize);
        await this.processBatch(batch);
        this.logger.log(`Processed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(issues.length / batchSize)}`);
      }

      this.logger.log('Finished updating existing issues.');
    } catch (error) {
      this.logger.error('Error updating existing issues:', error);
    }
  }

  private async processBatch(issues: Issue[]) {
    for (const issue of issues) {
      try {
        // Find the test result to get the testRunId
        const testResult = await this.testResultRepository.findOne({
          where: { id: issue.testResultId }
        });

        if (testResult && testResult.testRunId) {
          // Get the test run to find its project
          const testRun = await this.testRunRepository.findOne({
            where: { id: testResult.testRunId }
          });

          if (!testRun) {
            this.logger.warn(`Could not find test run with ID ${testResult.testRunId}`);
            continue;
          }

          // Get the project ID from the test run
          const projectId = testRun.projectId;

          // Find all test runs for this project
          const testRunsInProject = await this.testRunRepository.find({
            where: { projectId }
          });

          // Get all test run IDs for this project
          const testRunIds = testRunsInProject.map((tr: TestRun) => tr.id);

          // Find the highest defectId across all test runs in this project
          const maxDefectIdResult = await this.issueRepository
            .createQueryBuilder('issue')
            .select('MAX(issue.defectId)', 'maxDefectId')
            .where('issue.testRunId IN (:...testRunIds)', { testRunIds })
            .getRawOne();

          // Calculate the next defectId at the project level
          const nextDefectId = maxDefectIdResult && maxDefectIdResult.maxDefectId
            ? maxDefectIdResult.maxDefectId + 1
            : 1;

          // Update the issue
          issue.testRunId = testResult.testRunId;
          issue.defectId = nextDefectId;
          await this.issueRepository.save(issue);

          this.logger.debug(`Updated issue ${issue.id} with testRunId ${testResult.testRunId} and defectId ${nextDefectId}`);
        } else {
          this.logger.warn(`Could not find testRunId for issue ${issue.id} with testResultId ${issue.testResultId}`);
        }
      } catch (error) {
        this.logger.error(`Error updating issue ${issue.id}:`, error);
      }
    }
  }
}
