import { Enti<PERSON>, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, ManyToOne } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { TestResult, TestResultStatus } from './test-result.entity';

@Entity('test_result_history')
export class TestResultHistory {
  @ApiProperty({
    description: 'The unique identifier of the test result history',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => TestResult, { onDelete: 'CASCADE' })
  testResult: TestResult;

  @Column()
  testResultId: string;

  @ApiProperty({
    description: 'Previous test result status',
    enum: TestResultStatus,
    example: TestResultStatus.PASSED
  })
  @Column({
    type: 'enum',
    enum: TestResultStatus
  })
  previousStatus: TestResultStatus;

  @ApiProperty({
    description: 'Previous actual result',
    example: 'User was successfully redirected to dashboard'
  })
  @Column('text', { nullable: true })
  previousActualResult: string;

  @ApiProperty({
    description: 'Previous notes',
    example: 'Test failed due to network timeout'
  })
  @Column('text', { nullable: true })
  previousNotes: string;

  @ApiProperty({
    description: 'When the history record was created',
    example: '2024-03-17T10:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;
}