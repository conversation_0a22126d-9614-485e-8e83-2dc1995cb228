import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Project } from './project.entity';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';

@Injectable()
export class ProjectsService {
  constructor(
    @InjectRepository(Project)
    private projectsRepository: Repository<Project>,
  ) {}

  async findAll(userId: string, companyId?: string): Promise<any[]> {
    console.log(`Finding projects for user ${userId} and company ${companyId || 'none'}`);
    
    let projects: Project[];
    if (companyId) {
      projects = await this.projectsRepository.find({
        where: { companyId },
        order: { createdAt: 'DESC' },
        relations: ['user'],
      });
    } else {
      projects = await this.projectsRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
        relations: ['user'],
      });
    }
    // Map to include createdBy
    return projects.map(project => ({
      ...project,
      createdBy: project.user?.name || null,
    }));
  }

  async create(createProjectDto: CreateProjectDto, userId: string, companyId?: string): Promise<Project> {
    const project = this.projectsRepository.create({
      ...createProjectDto,
      userId,
      companyId
    });
    
    return this.projectsRepository.save(project);
  }

  async findOne(id: string, companyId: string): Promise<any> {
    const project = await this.projectsRepository.findOne({
      where: { id, companyId },
      relations: ['user'],
    });
    
    if (!project) {
      throw new NotFoundException(`Project with ID ${id} not found`);
    }
    
    // Add createdBy to response
    return {
      ...project,
      createdBy: project.user?.name || null,
    };
  }

  async update(id: string, userId: string, updateProjectDto: UpdateProjectDto): Promise<Project> {
    const project = await this.findOne(id, userId);
    
    Object.assign(project, updateProjectDto);
    
    return this.projectsRepository.save(project);
  }

  async remove(id: string, userId: string): Promise<void> {
    const project = await this.findOne(id, userId);
    
    await this.projectsRepository.remove(project);
  }
}
