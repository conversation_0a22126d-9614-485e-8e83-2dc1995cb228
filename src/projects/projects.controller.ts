import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, Query } from '@nestjs/common';
import { ProjectsService } from './projects.service';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Project } from './project.entity';

@ApiTags('projects')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('projects')
export class ProjectsController {
  constructor(private readonly projectsService: ProjectsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new project' })
  @ApiResponse({ status: 201, description: 'Project created successfully', type: Project })
  create(@Request() req, @Body() createProjectDto: CreateProjectDto, @Query('companyId') companyId?: string) {
    console.log(`POST /projects with companyId: ${companyId || 'none'}`);
    return this.projectsService.create(createProjectDto, req.user.id, companyId || req.user.companyId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all projects' })
  @ApiResponse({ status: 200, description: 'Return all projects', type: [Project] })
  async findAll(
    @Request() req, 
    @Query('companyId') companyId?: string
  ) {
    console.log(`GET /projects with companyId: ${companyId || 'none'}`);
    const userId = req.user.id;
    
    // Use companyId from query, or from user object if available
    const effectiveCompanyId = companyId || req.user.companyId;
    console.log(`Using effective companyId: ${effectiveCompanyId || 'none'}`);
    
    try {
      const projects = await this.projectsService.findAll(userId, effectiveCompanyId);
      console.log(`Found ${projects.length} projects`);
      
      // Return in a consistent format that the frontend expects
      return {
        projects: projects,
        total: projects.length,
        totalPages: 1
      };
    } catch (error) {
      console.error('Error in findAll:', error);
      throw error;
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get project by id' })
  @ApiResponse({ status: 200, description: 'Return the project', type: Project })
  @ApiResponse({ status: 404, description: 'Project not found.' })
  findOne(@Request() req, @Param('id') id: string) {
    return this.projectsService.findOne(id, req.user.companyId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a project' })
  @ApiResponse({ status: 200, description: 'Project updated successfully', type: Project })
  @ApiResponse({ status: 404, description: 'Project not found.' })
  update(@Request() req, @Param('id') id: string, @Body() updateProjectDto: UpdateProjectDto) {
    return this.projectsService.update(id, req.user.companyId, updateProjectDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a project' })
  @ApiResponse({ status: 200, description: 'Project deleted successfully' })
  @ApiResponse({ status: 404, description: 'Project not found.' })
  remove(@Request() req, @Param('id') id: string) {
    // Get companyId from request user
    const companyId = req.user.companyId;
    console.log(`Deleting project ${id} for company ${companyId}`);
    
    return this.projectsService.remove(id, companyId);
  }
}
