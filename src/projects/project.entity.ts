import { <PERSON>tity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../users/user.entity';
import { Company } from '../companies/entities/company.entity';

@Entity('projects')
export class Project {

  @ApiProperty({
    description: 'The unique identifier of the project',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Project name',
    example: 'E-commerce Platform'
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'Project description',
    example: 'A comprehensive e-commerce solution'
  })
  @Column({ type: 'text', nullable: true })
  description: string;

  @ApiProperty({
    description: 'The user who owns this project',
    type: () => User
  })
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @ApiProperty({
    description: 'The ID of the user who owns this project',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column()
  userId: string;

  @ApiProperty({
    description: 'The company this project belongs to',
    type: () => Company
  })
  @ManyToOne(() => Company, company => company.projects, { nullable: true })
  @JoinColumn({ name: 'companyId' })
  company: Company;

  @Column({ nullable: true })
  companyId: string;

  @ApiProperty({
    description: 'When the project was created',
    example: '2024-02-20T12:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When the project was last updated',
    example: '2024-02-20T12:00:00Z'
  })
  @UpdateDateColumn()
  updatedAt: Date;
}
