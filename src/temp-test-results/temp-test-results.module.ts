import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TempTestResultsService } from './temp-test-results.service';
import { TempTestResultsController } from './temp-test-results.controller';
import { TempTestResult } from './temp-test-result.entity';
import { StorageService } from './storage.service';
import { TestCasesModule } from '../test-cases/test-cases.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([TempTestResult]),
    TestCasesModule
  ],
  controllers: [TempTestResultsController],
  providers: [TempTestResultsService, StorageService],
  exports: [TempTestResultsService, StorageService],
})
export class TempTestResultsModule {}
