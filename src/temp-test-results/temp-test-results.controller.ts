import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  HttpStatus,
  HttpCode,
  Query,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { TempTestResultsService } from './temp-test-results.service';
import { TestCasesService } from '../test-cases/test-cases.service';
import { CreateTempTestResultDto } from './dto/create-temp-test-result.dto';
import { TempTestResult } from './temp-test-result.entity';
import { FileInterceptor } from '@nestjs/platform-express';
import * as Multer from 'multer';

@ApiTags('temp-test-results')
@Controller('temp-test-results')
export class TempTestResultsController {
  constructor(
    private readonly tempTestResultsService: TempTestResultsService,
    private readonly testCasesService: TestCasesService,
  ) {}

  @Post()
  async create(@Body() createTempTestResultDto: CreateTempTestResultDto) {
    // If projectId is missing but testCaseId is present, try to fetch it
    if (!createTempTestResultDto.projectId && createTempTestResultDto.testCaseId) {
      try {
        // Use the direct repository query instead of the service method that requires additional params
        const testCase = await this.testCasesService.findOneBasic(createTempTestResultDto.testCaseId);
        if (testCase && testCase.projectId) {
          createTempTestResultDto.projectId = testCase.projectId;
        }
      } catch (error) {
        console.error('Failed to fetch projectId from testCaseId:', error);
      }
    }
    
    // Check if there's an existing result for this test case
    if (createTempTestResultDto.testCaseId) {
      try {
        const existingResult = await this.tempTestResultsService.findLatestByTestCase(createTempTestResultDto.testCaseId);
        
        // If there's an existing result, update it instead of creating a new one
        if (existingResult) {
          return this.tempTestResultsService.update(existingResult.id, createTempTestResultDto);
        }
      } catch (error) {
        console.error('Failed to check for existing test results:', error);
        // Continue with creating a new result if there was an error checking for existing ones
      }
    }
    
    // If no existing result was found or there was an error, create a new one
    return this.tempTestResultsService.create(createTempTestResultDto);
  }

  @Get('test-case/:testCaseId')
  @ApiOperation({ summary: 'Get all temp test results for a test case' })
  @ApiParam({
    name: 'testCaseId',
    description: 'Test case ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Temp test results retrieved successfully',
    type: [TempTestResult],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Test case not found' })
  async findByTestCase(
    @Param('testCaseId') testCaseId: string,
  ): Promise<TempTestResult[]> {
    return this.tempTestResultsService.findByTestCase(testCaseId);
  }

  @Get('project/:projectId')
  @ApiOperation({ summary: 'Get all temp test results for a project' })
  @ApiParam({
    name: 'projectId',
    description: 'Project ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Temp test results retrieved successfully',
    type: [TempTestResult],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  async findByProject(
    @Param('projectId') projectId: string,
  ): Promise<TempTestResult[]> {
    return this.tempTestResultsService.findByProject(projectId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific temp test result' })
  @ApiParam({
    name: 'id',
    description: 'Temp test result ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Temp test result retrieved successfully',
    type: TempTestResult,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Temp test result not found' })
  async findOne(@Param('id') id: string): Promise<TempTestResult> {
    return this.tempTestResultsService.findOne(id);
  }

  @Get(':id/logs')
  @ApiOperation({ summary: 'Get logs for a specific temp test result' })
  @ApiParam({
    name: 'id',
    description: 'Temp test result ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Logs retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        logs: {
          type: 'array',
          items: { type: 'string' },
          example: ['🔑 Fetching AgentQ API key...', '🔗 Connecting to WebSocket server...'],
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Temp test result not found' })
  async getLogs(@Param('id') id: string): Promise<{ logs: string[] }> {
    const logs = await this.tempTestResultsService.getLogs(id);
    return { logs };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a temp test result' })
  @ApiParam({
    name: 'id',
    description: 'Temp test result ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({ status: 204, description: 'Temp test result deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Temp test result not found' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string): Promise<void> {
    return this.tempTestResultsService.remove(id);
  }

  @Delete('cleanup')
  @ApiOperation({ summary: 'Clean up old temp test results' })
  @ApiQuery({
    name: 'daysOld',
    description: 'Number of days old to clean up (default: 30)',
    required: false,
    example: 30,
  })
  @ApiResponse({
    status: 200,
    description: 'Cleanup completed successfully',
    schema: {
      type: 'object',
      properties: {
        deletedCount: {
          type: 'number',
          example: 15,
        },
        message: {
          type: 'string',
          example: 'Cleaned up 15 old test results',
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async cleanup(
    @Query('daysOld') daysOld?: number,
  ): Promise<{ deletedCount: number; message: string }> {
    const deletedCount = await this.tempTestResultsService.cleanupOldResults(
      daysOld || 30,
    );
    return {
      deletedCount,
      message: `Cleaned up ${deletedCount} old test results`,
    };
  }

  @Get(':id/video')
  @ApiOperation({ summary: 'Get video URL for a test result' })
  @ApiResponse({ status: 200, description: 'Returns a signed URL for the video' })
  @ApiResponse({ status: 404, description: 'Test result not found' })
  async getVideo(@Param('id') id: string): Promise<{ videoUrl: string }> {
    const videoUrl = await this.tempTestResultsService.getVideoUrl(id);
    return { videoUrl };
  }

  @Post(':id/video')
  @ApiOperation({ summary: 'Upload video for a test result' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadVideo(
    @Param('id') id: string,
    @UploadedFile() file: Multer.File,
  ): Promise<{ videoUrl: string }> {
    const videoUrl = await this.tempTestResultsService.uploadVideo(
      id,
      file.buffer,
      file.mimetype,
    );
    return { videoUrl };
  }

  @Put(':id')
  async update(@Param('id') id: string, @Body() updateDto: any) {
    return this.tempTestResultsService.update(id, updateDto);
  }

  @Post('security-logs')
  @ApiOperation({ summary: 'Store security logs (ZAP report) for a test result' })
  @ApiResponse({ status: 200, description: 'Security logs stored successfully' })
  @ApiResponse({ status: 404, description: 'Test result not found' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  async storeSecurityLogs(
    @Body() body: { testCaseId: string; zapReport: any }
  ) {
    const result = await this.tempTestResultsService.storeSecurityLogsByTestCaseId(
      body.testCaseId,
      body.zapReport
    );

    if (!result) {
      return {
        success: false,
        message: 'No test result found for the specified test case'
      };
    }

    return {
      success: true,
      message: 'Security logs stored successfully',
      data: {
        testResultId: result.id,
        logsSecurityUrl: result.logsSecurityUrl
      }
    };
  }

  @Delete('security-reports/:testCaseId')
  @ApiOperation({ summary: 'Clear stored security reports for a test case' })
  @ApiParam({
    name: 'testCaseId',
    description: 'Test case ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({ status: 200, description: 'Security reports cleared successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async clearSecurityReports(@Param('testCaseId') testCaseId: string): Promise<{ success: boolean; message: string }> {
    await this.tempTestResultsService.clearSecurityReports(testCaseId);
    return {
      success: true,
      message: `Security reports cleared for test case: ${testCaseId}`
    };
  }
}
