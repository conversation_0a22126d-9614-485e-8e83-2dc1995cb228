import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { HealthCheck, HealthCheckService } from '@nestjs/terminus';
import { HealthService } from './health.service';
import { SystemMetricsService } from './system-metrics.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private healthService: HealthService,
    private systemMetricsService: SystemMetricsService,
  ) {}

  @Get()
  @HealthCheck()
  @ApiOperation({ summary: 'Check the health of the application' })
  @ApiResponse({ 
    status: 200, 
    description: 'The application is healthy',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        info: { 
          type: 'object',
          properties: {
            database: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'up' }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 503, description: 'The application is not healthy' })
  check() {
    return this.health.check([
      () => this.healthService.checkDatabase(),
      () => this.healthService.checkMemory(),
    ]);
  }

  @Get('detailed')
  @ApiOperation({ summary: 'Get detailed health metrics' })
  @ApiResponse({ 
    status: 200, 
    description: 'Detailed health metrics',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', example: '2023-05-01T12:00:00Z' },
        memory: {
          type: 'object',
          properties: {
            heapTotal: { type: 'number', example: 51.45 },
            heapUsed: { type: 'number', example: 26.27 },
            rss: { type: 'number', example: 81.59 },
            memoryUsagePercent: { type: 'number', example: 42.5 }
          }
        },
        cpu: {
          type: 'object',
          properties: {
            system: { type: 'number', example: 2.56 },
            process: { type: 'number', example: 0.45 },
            cpuCount: { type: 'number', example: 8 }
          }
        },
        uptime: { type: 'number', example: 3600 },
        database: {
          type: 'object',
          properties: {
            status: { type: 'string', example: 'up' },
            responseTime: { type: 'number', example: 5 }
          }
        }
      }
    }
  })
  @ApiQuery({ name: 'full', required: false, type: Boolean, description: 'Include full system metrics' })
  async getDetailedHealth(@Query('full') full?: boolean) {
    const metrics = await this.systemMetricsService.getSystemMetrics();
    const dbHealth = await this.healthService.checkDatabaseDetailed();
    
    const result = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      memory: metrics.memory,
      cpu: metrics.cpu,
      uptime: metrics.uptime,
      database: dbHealth,
    };
    
    if (full) {
      return {
        ...result,
        system: metrics.system,
        process: metrics.process,
      };
    }
    
    return result;
  }
}
