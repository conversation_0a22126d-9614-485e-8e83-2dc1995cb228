import { Injectable } from '@nestjs/common';
import * as os from 'os';

@Injectable()
export class SystemMetricsService {
  private startTime: number;

  constructor() {
    this.startTime = Date.now();
  }

  async getSystemMetrics(): Promise<any> {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    const systemUptime = os.uptime();
    const processUptime = process.uptime();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const cpuCount = os.cpus().length;
    
    // Calculate CPU usage percentage
    const startUsage = process.cpuUsage();
    // Wait 100ms to get a meaningful CPU usage sample
    await new Promise(resolve => setTimeout(resolve, 100));
    const endUsage = process.cpuUsage(startUsage);
    const userCPUUsage = endUsage.user / 1000; // microseconds to milliseconds
    const systemCPUUsage = endUsage.system / 1000; // microseconds to milliseconds
    
    // Calculate CPU usage percentage (approximation)
    const totalCPUUsage = userCPUUsage + systemCPUUsage;
    const elapsedTime = 100; // milliseconds we waited
    const cpuPercent = (totalCPUUsage / (elapsedTime * cpuCount)) * 100;
    
    // Convert memory values to MB for readability
    const heapUsed = Math.round((memoryUsage.heapUsed / 1024 / 1024) * 100) / 100;
    const heapTotal = Math.round((memoryUsage.heapTotal / 1024 / 1024) * 100) / 100;
    const rss = Math.round((memoryUsage.rss / 1024 / 1024) * 100) / 100;
    const external = Math.round((memoryUsage.external / 1024 / 1024) * 100) / 100;
    const arrayBuffers = Math.round((memoryUsage.arrayBuffers / 1024 / 1024) * 100) / 100;
    
    // Calculate memory usage percentage
    const memoryUsagePercent = Math.round((1 - (freeMemory / totalMemory)) * 100);
    const heapUsagePercent = Math.round((heapUsed / heapTotal) * 100);
    
    return {
      memory: {
        heapTotal,
        heapUsed,
        rss,
        external,
        arrayBuffers,
        heapUsagePercent,
        memoryUsagePercent,
        totalMemoryMB: Math.round(totalMemory / 1024 / 1024),
        freeMemoryMB: Math.round(freeMemory / 1024 / 1024),
      },
      cpu: {
        system: Math.round(systemCPUUsage * 100) / 100,
        user: Math.round(userCPUUsage * 100) / 100,
        percent: Math.round(cpuPercent * 100) / 100,
        cpuCount,
      },
      uptime: Math.round(processUptime),
      system: {
        platform: os.platform(),
        release: os.release(),
        hostname: os.hostname(),
        type: os.type(),
        arch: os.arch(),
        uptime: Math.round(systemUptime),
        loadavg: os.loadavg().map(load => Math.round(load * 100) / 100),
      },
      process: {
        pid: process.pid,
        ppid: process.ppid,
        title: process.title,
        nodeVersion: process.version,
        startTime: new Date(this.startTime).toISOString(),
        uptime: Math.round(processUptime),
      },
    };
  }
}
