import { Injectable } from '@nestjs/common';
import { InjectConnection } from '@nestjs/typeorm';
import { Connection } from 'typeorm';
import { HealthCheckError, HealthIndicator, HealthIndicatorResult } from '@nestjs/terminus';

@Injectable()
export class HealthService extends HealthIndicator {
  constructor(
    @InjectConnection() private connection: Connection,
  ) {
    super();
  }

  async checkDatabase(): Promise<HealthIndicatorResult> {
    try {
      const startTime = Date.now();
      await this.connection.query('SELECT 1');
      const responseTime = Date.now() - startTime;
      
      return this.getStatus('database', true, { responseTime });
    } catch (error) {
      throw new HealthCheckError(
        'Database health check failed',
        this.getStatus('database', false, { message: (error as Error).message }),
      );
    }
  }

  async checkDatabaseDetailed(): Promise<any> {
    try {
      const startTime = Date.now();
      await this.connection.query('SELECT 1');
      const responseTime = Date.now() - startTime;
      
      // Get active connections count
      const activeConnectionsResult = await this.connection.query(
        "SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active'"
      );
      const activeConnections = parseInt(activeConnectionsResult[0]?.count || '0', 10);
      
      // Get database size
      const dbSizeResult = await this.connection.query(
        `SELECT pg_size_pretty(pg_database_size(current_database())) as size, 
         pg_database_size(current_database()) as bytes`
      );
      const dbSize = dbSizeResult[0]?.size || 'unknown';
      const dbSizeBytes = parseInt(dbSizeResult[0]?.bytes || '0', 10);
      
      return {
        status: 'up',
        responseTime,
        activeConnections,
        size: {
          formatted: dbSize,
          bytes: dbSizeBytes
        }
      };
    } catch (error) {
      return {
        status: 'down',
        error: (error as Error).message,
      };
    }
  }

  async checkMemory(): Promise<HealthIndicatorResult> {
    const memoryUsage = process.memoryUsage();
    
    // Convert to MB for readability
    const heapUsed = Math.round((memoryUsage.heapUsed / 1024 / 1024) * 100) / 100;
    const heapTotal = Math.round((memoryUsage.heapTotal / 1024 / 1024) * 100) / 100;
    const memoryUsagePercent = Math.round((heapUsed / heapTotal) * 100);
    
    // Consider memory unhealthy if usage is above 90%
    const isHealthy = memoryUsagePercent < 90;
    
    return this.getStatus('memory', isHealthy, {
      heapUsed,
      heapTotal,
      memoryUsagePercent,
    });
  }
}
