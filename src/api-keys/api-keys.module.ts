import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Api<PERSON>eysController } from './api-keys.controller';
import { ApiKeysService } from './api-keys.service';
import { <PERSON>pi<PERSON><PERSON> } from './api-key.entity';
import { User } from '../users/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ApiKey, User])],
  controllers: [ApiKeysController],
  providers: [ApiKeysService],
  exports: [ApiKeysService],
})
export class ApiKeysModule {}
