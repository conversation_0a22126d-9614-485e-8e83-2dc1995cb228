import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../users/user.entity';

@Entity('api_keys')
export class ApiKey {
  @ApiProperty({
    description: 'The unique identifier of the API key',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The provider for this API key',
    example: 'agentq'
  })
  @Column()
  provider: string;

  @ApiProperty({
    description: 'The API key value',
    example: 'sk-1234567890abcdef'
  })
  @Column()
  apiKey: string;

  @ApiProperty({
    description: 'The user who owns this API key',
    type: () => User
  })
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  user: User;

  @ApiProperty({
    description: 'The ID of the user who owns this API key',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column()
  userId: string;

  @ApiProperty({
    description: 'The ID of the company this API key belongs to',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ nullable: true })
  companyId: string;

  @ApiProperty({
    description: 'When the API key was created',
    example: '2024-02-20T12:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When the API key was last updated',
    example: '2024-02-20T12:00:00Z'
  })
  @UpdateDateColumn()
  updatedAt: Date;
}
