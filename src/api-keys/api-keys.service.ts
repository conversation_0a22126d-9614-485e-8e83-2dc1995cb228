import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApiKey } from './api-key.entity';
import { CreateApiKeyDto } from './dto/create-api-key.dto';
import { UpdateApiKeyDto } from './dto/update-api-key.dto';
import { User } from '../users/user.entity';

@Injectable()
export class ApiKeysService {
  constructor(
    @InjectRepository(ApiKey)
    private apiKeysRepository: Repository<ApiKey>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
  ) {}

  async create(userId: string, createApiKeyDto: CreateApiKeyDto): Promise<ApiKey> {
    // Get the user to find their company ID if not provided
    let companyId = createApiKeyDto.companyId;
    
    if (!companyId) {
      const user = await this.usersRepository.findOne({ where: { id: userId } });
      companyId = user?.companyId;
    }
    
    // Create the API key with user ID and company ID
    const apiKey = this.apiKeysRepository.create({
      ...createApiKeyDto,
      userId,
      companyId,
    });
    
    return this.apiKeysRepository.save(apiKey);
  }

  async findAll(userId: string): Promise<ApiKey[]> {
    // First, get the user to find their company ID
    const user = await this.usersRepository.findOne({ where: { id: userId } });
    
    if (!user || !user.companyId) {
      return []; // Return empty array if user not found or has no company
    }
    
    // Return all API keys for the user's company
    return this.apiKeysRepository.find({ 
      where: { companyId: user.companyId } 
    });
  }

  async findOne(id: string, userId: string): Promise<ApiKey> {
    // First, get the user to find their company ID
    const user = await this.usersRepository.findOne({ where: { id: userId } });
    
    if (!user || !user.companyId) {
      throw new NotFoundException('User not found or has no company');
    }
    
    // Find API key by ID and company ID
    const apiKey = await this.apiKeysRepository.findOne({ 
      where: { id, companyId: user.companyId } 
    });
    
    if (!apiKey) {
      throw new NotFoundException('API key not found');
    }
    
    return apiKey;
  }

  async update(id: string, userId: string, updateApiKeyDto: UpdateApiKeyDto): Promise<ApiKey> {
    const apiKey = await this.findOne(id, userId);
    
    // Create a new object without the companyId field
    const { companyId, ...updateData } = updateApiKeyDto;
    
    Object.assign(apiKey, updateData);
    return this.apiKeysRepository.save(apiKey);
  }

  async remove(id: string, userId: string): Promise<void> {
    const apiKey = await this.findOne(id, userId);
    await this.apiKeysRepository.remove(apiKey);
  }
}
