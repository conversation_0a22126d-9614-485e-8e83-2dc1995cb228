import { IsNotEmpty, <PERSON><PERSON>tring, IsEmail, IsUrl, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateIntegrationDto {
  @ApiProperty({
    description: 'The type of integration (e.g., confluence)',
    example: 'confluence'
  })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty({
    description: 'Email address for the integration',
    example: '<EMAIL>'
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'API token for the integration',
    example: 'abc123xyz'
  })
  @IsString()
  @IsNotEmpty()
  apiToken: string;

  @ApiPropertyOptional({
    description: 'JIRA URL/Domain',
    example: 'https://my-site.atlassian.net'
  })
  @IsUrl({ require_tld: false }, { message: 'jiraUrl must be a valid URL' })
  @IsOptional()
  jiraUrl?: string;

  @ApiPropertyOptional({
    description: 'The company ID this integration belongs to',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @IsString()
  @IsOptional()
  companyId?: string;
}
