import { IsEmail, IsString, IsOptional, IsUrl } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateIntegrationDto {
  @ApiPropertyOptional({
    description: 'The type of integration (e.g., confluence)',
    example: 'confluence'
  })
  @IsString()
  @IsOptional()
  type?: string;

  @ApiPropertyOptional({
    description: 'Email address for the integration',
    example: '<EMAIL>'
  })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({
    description: 'API token for the integration',
    example: 'abc123xyz'
  })
  @IsString()
  @IsOptional()
  apiToken?: string;

  @ApiPropertyOptional({
    description: 'JIRA URL/Domain',
    example: 'https://my-site.atlassian.net'
  })
  @IsUrl({ require_tld: false }, { message: 'jiraUrl must be a valid URL' })
  @IsOptional()
  jiraUrl?: string;
}