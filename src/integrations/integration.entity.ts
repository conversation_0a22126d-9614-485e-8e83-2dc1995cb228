import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { User } from '../users/user.entity';

@Entity('integrations')
export class Integration {
  @ApiProperty({
    description: 'The unique identifier of the integration',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The type of integration',
    example: 'atlassian'
  })
  @Column()
  type: string;

  @ApiProperty({
    description: 'Email address for the integration',
    example: '<EMAIL>'
  })
  @Column()
  email: string;

  @ApiProperty({
    description: 'API token for the integration',
    example: 'abc123xyz'
  })
  @Column()
  apiToken: string;

  @ApiProperty({
    description: 'JIRA URL/Domain',
    example: 'https://my-site.atlassian.net'
  })
  @Column({ nullable: true })
  jiraUrl?: string;

  @ApiProperty({
    description: 'The user who owns this integration',
    type: () => User
  })
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  user: User;

  @ApiProperty({
    description: 'The ID of the user who owns this integration',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column()
  userId: string;

  @ApiProperty({
    description: 'The ID of the company this integration belongs to',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ nullable: true })
  companyId: string;

  @ApiProperty({
    description: 'When the integration was created',
    example: '2024-02-20T12:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When the integration was last updated',
    example: '2024-02-20T12:00:00Z'
  })
  @UpdateDateColumn()
  updatedAt: Date;
}
