import { Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Integration } from './integration.entity';
import { CreateIntegrationDto } from './dto/create-integration.dto';
import { UpdateIntegrationDto } from './dto/update-integration.dto';
import { UsersService } from '../users/user.service';
import { User } from '../users/user.entity';

@Injectable()
export class IntegrationsService {
  constructor(
    @InjectRepository(Integration)
    private integrationsRepository: Repository<Integration>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private usersService: UsersService,
  ) {}

  async create(userId: string, createIntegrationDto: CreateIntegrationDto): Promise<Integration> {
    // Get the user to find their company ID if not provided
    let companyId = createIntegrationDto.companyId;
    
    if (!companyId) {
      const user = await this.usersService.findOne(userId);
      companyId = user?.companyId;
    }
    
    // Create the integration with user ID and company ID
    const integration = this.integrationsRepository.create({
      ...createIntegrationDto,
      userId,
      companyId,
    });
    
    return this.integrationsRepository.save(integration);
  }

  async findAll(userId: string): Promise<Integration[]> {
    // First, get the user to find their company ID
    const user = await this.usersRepository.findOne({ where: { id: userId } });
    
    if (!user || !user.companyId) {
      return []; // Return empty array if user not found or has no company
    }
    
    // Return all integrations for the user's company
    return this.integrationsRepository.find({ 
      where: { companyId: user.companyId } 
    });
  }

  async findOne(id: string, userId: string): Promise<Integration> {
    // First, get the user to find their company ID
    const user = await this.usersRepository.findOne({ where: { id: userId } });
    
    if (!user || !user.companyId) {
      throw new NotFoundException('User not found or has no company');
    }
    
    // Find integration by ID and company ID
    const integration = await this.integrationsRepository.findOne({ 
      where: { id, companyId: user.companyId } 
    });
    
    if (!integration) {
      throw new NotFoundException('Integration not found');
    }
    
    return integration;
  }

  async update(id: string, userId: string, updateIntegrationDto: UpdateIntegrationDto): Promise<Integration> {
    const integration = await this.findOne(id, userId);
    
    Object.assign(integration, updateIntegrationDto);
    return this.integrationsRepository.save(integration);
  }

  async remove(id: string, userId: string): Promise<void> {
    const integration = await this.findOne(id, userId);
    await this.integrationsRepository.remove(integration);
  }
}
