import { Controller, Get, UseGuards, Param, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JiraService } from './jira.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';

@ApiTags('integrations')
@ApiBearerAuth()
@Controller('integrations/jira')
@UseGuards(JwtAuthGuard)
export class JiraController {
  constructor(private readonly jiraService: JiraService) {}

  @Get('projects')
  @ApiOperation({ summary: 'Get JIRA projects' })
  @ApiResponse({
    status: 200,
    description: 'List of JIRA projects',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          key: { type: 'string' },
          name: { type: 'string' }
        }
      }
    }
  })
  async getProjects(@Request() req) {
    return this.jiraService.getProjects(req.user.companyId);
  }

  @Get('priorities')
  @ApiOperation({ summary: 'Get JIRA priorities' })
  @ApiResponse({
    status: 200,
    description: 'List of JIRA priorities',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          iconUrl: { type: 'string' }
        }
      }
    }
  })
  async getPriorities(@Request() req) {
    return this.jiraService.getPriorities(req.user.companyId);
  }

  @Get('projects/:projectId/issuetypes')
  @ApiOperation({ summary: 'Get issue types for a JIRA project' })
  @ApiResponse({
    status: 200,
    description: 'List of issue types',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          iconUrl: { type: 'string' }
        }
      }
    }
  })
  async getIssueTypes(@Param('projectId') projectId: string, @Request() req) {
    return this.jiraService.getIssueTypes(projectId, req.user.companyId);
  }

  @Get('projects/:projectId/priorities')
  @ApiOperation({ summary: 'Get priorities for a JIRA project' })
  @ApiResponse({
    status: 200,
    description: 'List of priorities for the project',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          iconUrl: { type: 'string' }
        }
      }
    }
  })
  async getProjectPriorities(@Param('projectId') projectId: string, @Request() req) {
    return this.jiraService.getProjectPriorities(projectId, req.user.companyId);
  }

  @Get('projects/:projectId/customfields')
  @ApiOperation({ summary: 'Get custom fields for a JIRA project' })
  @ApiResponse({
    status: 200,
    description: 'List of custom fields for the project',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          type: { type: 'string' },
          required: { type: 'boolean' },
          allowedValues: {
            type: 'array',
            items: {
              type: 'object'
            }
          }
        }
      }
    }
  })
  async getProjectCustomFields(@Param('projectId') projectId: string, @Request() req) {
    return this.jiraService.getProjectCustomFields(projectId, req.user.companyId);
  }

  @Get('projects/:projectId/customfields/:fieldId/values')
  @ApiOperation({ summary: 'Get allowed values for a specific custom field' })
  @ApiResponse({
    status: 200,
    description: 'List of allowed values for the custom field',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          value: { type: 'string' }
        }
      }
    }
  })
  async getCustomFieldValues(
    @Param('projectId') projectId: string,
    @Param('fieldId') fieldId: string,
    @Request() req
  ) {
    return this.jiraService.getCustomFieldValues(projectId, fieldId, req.user.companyId);
  }
}