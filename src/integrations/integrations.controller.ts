import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { IntegrationsService } from './integrations.service';
import { CreateIntegrationDto } from './dto/create-integration.dto';
import { UpdateIntegrationDto } from './dto/update-integration.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';
import { Integration } from './integration.entity';

@ApiTags('integrations')
@ApiBearerAuth()
@Controller('integrations')
@UseGuards(JwtAuthGuard)
export class IntegrationsController {
  constructor(private readonly integrationsService: IntegrationsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new integration' })
  @ApiResponse({ status: 201, description: 'Integration created successfully', type: Integration })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  create(@Request() req, @Body() createIntegrationDto: CreateIntegrationDto) {
    return this.integrationsService.create(req.user.id, createIntegrationDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all integrations for the current user' })
  @ApiResponse({ status: 200, description: 'List of integrations', type: [Integration] })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  findAll(@Request() req) {
    return this.integrationsService.findAll(req.user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific integration by ID' })
  @ApiResponse({ status: 200, description: 'Integration found', type: Integration })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Integration not found' })
  findOne(@Request() req, @Param('id') id: string) {
    return this.integrationsService.findOne(id, req.user.id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an integration' })
  @ApiResponse({ status: 200, description: 'Integration updated successfully', type: Integration })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Integration not found' })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateIntegrationDto: UpdateIntegrationDto,
  ) {
    return this.integrationsService.update(id, req.user.id, updateIntegrationDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an integration' })
  @ApiResponse({ status: 200, description: 'Integration deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Integration not found' })
  remove(@Request() req, @Param('id') id: string) {
    return this.integrationsService.remove(id, req.user.id);
  }
}