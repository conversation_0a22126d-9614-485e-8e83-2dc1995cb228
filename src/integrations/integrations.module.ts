import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IntegrationsService } from './integrations.service';
import { IntegrationsController } from './integrations.controller';
import { Integration } from './integration.entity';
import { JiraService } from './jira.service';
import { <PERSON>ra<PERSON>ontroller } from './jira.controller';
import { User } from '../users/user.entity';
import { UsersModule } from '../users/user.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Integration, User]),
    UsersModule
  ],
  controllers: [IntegrationsController, JiraController],
  providers: [IntegrationsService, JiraService],
  exports: [JiraService],
})
export class IntegrationsModule {}
