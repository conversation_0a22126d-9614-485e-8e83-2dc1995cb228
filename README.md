# AgentQ Backend Microservice

This is the backend microservice for AgentQ, built with NestJS.

## Setup

1. Install dependencies:
```bash
npm install
```

2. Create a `.env` file with the following content:
```
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_DATABASE=agentq_opensource

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_change_in_production

# Service URLs
INTEGRATION_SERVICE_URL=http://localhost:3004
```

3. Start the development server:
```bash
npm run start:dev
```

4. Build for production:
```bash
npm run build
```

## Features

- User authentication and authorization
- Project management
- Test case management
- Test run execution and reporting
- API key management
- Health monitoring

## API Documentation

Swagger documentation is available at http://localhost:3010/api when the server is running.

## Port

The backend runs on port 3010.
