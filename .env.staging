NODE_ENV=staging

# Database Configuration
DB_HOST=***********
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=azq"D0sVt`<l{PkE
DB_DATABASE=agentq_enterprise

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_change_in_production

# Google Cloud Storage Configuration
ENABLE_CLOUD_STORAGE=true
GCP_PROJECT_ID=orbital-nirvana-447600-j8
GCP_CLIENT_EMAIL=<EMAIL>
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
GCP_BUCKET_NAME=agentq

ENABLE_CLOUD_STORAGE=true